package com.facishare.marketing.api.result.wxcoupon;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/21 14:24
 */
@Data
public class PartnerReceiveResult implements Serializable {

    @ApiModelProperty
    private String outUid;

    @ApiModelProperty(value = "公司名称")
    private String businessName;

    @ApiModelProperty(value = "领取时间")
    private String receiveTime;

    @ApiModelProperty(value = "存入对象")
    private Boolean depositObject;

    @ApiModelProperty(value = "使用状态 1:可用 2:已使用 3:已过期")
    private String status;
}
