package com.facishare.marketing.api.result.qywx.card;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class PhotoListUnitResult implements Serializable{

    @ApiModelProperty(value = "图片id", notes = "图片id")
    private String id;

    @ApiModelProperty(value = "缩略图", notes = "缩略图")
    private String thumbnailUrl;

    @ApiModelProperty(value = "原图", notes = "原图")
    private String url;
}
