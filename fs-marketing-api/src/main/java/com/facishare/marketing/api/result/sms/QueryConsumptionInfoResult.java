package com.facishare.marketing.api.result.sms;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by ranluch on 2019/3/25.
 */
@Data
public class QueryConsumptionInfoResult implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("消耗总量")
    private Integer consumeTotal;

    @ApiModelProperty("分渠道消耗统计列表")
    private List<ConsumptionStat> consumeList;

    @Data
    public static class ConsumptionStat implements Serializable {

        /**
         * {@link com.facishare.marketing.common.enums.sms.ChannelTypeEnum}
         */
        @ApiModelProperty("使用方类型：1营销通，2服务通")
        private Integer channelType;
        @ApiModelProperty("使用方名称")
        private String channelName;
        @ApiModelProperty("消耗数量")
        private Integer consumeCnt;
    }
}
