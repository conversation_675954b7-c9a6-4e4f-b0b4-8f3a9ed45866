package com.facishare.marketing.api.result.qywx;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/23 17:13
 */
@Data
public class SendMomentCustomerResult implements Serializable {

    @ApiModelProperty("计算任务ID")
    private String calculationTaskId;

    @ApiModelProperty("选择员工数")
    private Integer selectEmployeeCount;

    @ApiModelProperty("发送员工数")
    private Integer employeeCount;

    @ApiModelProperty("发送客户数")
    private Integer customerCount;
}
