package com.facishare.marketing.api.result.advertiser.headlines;


import com.facishare.marketing.common.result.PageResult;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2021/08/03
 */
@Data
public class HeadlinesCampaignInfoResult extends PageResult {
    @ApiModelProperty("在营销通中的id")
    private String id;
    @ApiModelProperty("广告组id")
    private Long campaignId;
    @ApiModelProperty("广告组名称")
    private String campaignName;
    @ApiModelProperty("广告组预算")
    private Double budget;
    @ApiModelProperty("广告组预算类型")
    private String budgetMode;
    @ApiModelProperty("推广目的")
    private String landingType;
    @ApiModelProperty("广告组状态")
    private String status;
    @ApiModelProperty("营销目的")
    private String marketingPurpose;
    @ApiModelProperty("投放类型")
    private String deliveryMode;
    @ApiModelProperty("数据同步时间")
    private Long refreshTime;
}
