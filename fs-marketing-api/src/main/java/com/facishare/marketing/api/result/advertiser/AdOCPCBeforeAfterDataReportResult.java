package com.facishare.marketing.api.result.advertiser;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class AdOCPCBeforeAfterDataReportResult implements Serializable {

    private String adReturnBeginTime;

    private List<Item> beforeList;

    private List<Item> afterList;

    private BigDecimal beforeAverageCoverageCost;

    private BigDecimal afterAverageCoverageCost;

    @Data
    public static class Item implements Serializable {

        private String date;

        private BigDecimal convertCost;
    }

}
