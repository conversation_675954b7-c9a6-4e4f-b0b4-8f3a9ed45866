package com.facishare.marketing.api.result.advertiser;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("广告线索数据")
public class AdLeadDataResult implements Serializable {

    @ApiModelProperty("省份")
    private String province;

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("城市")
    private String city;
}
