package com.facishare.marketing.api.result.account;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName AccountIsApplyForKISResult
 * @Description
 * <AUTHOR>
 * @Date 2019/2/25 3:31 PM
 */
@Data
public class AccountIsApplyForKISResult implements Serializable {
    /**
     *  开通状态 {@link com.facishare.marketing.common.enums.account.ApplyBindStatusForKISEnum}
     */
    private int status;

    private String phone;
    private String bindCompanyName;
    private String applyInfoKey;

    private String uid;

    private String miniappVisitType;

    private String userType;
}
