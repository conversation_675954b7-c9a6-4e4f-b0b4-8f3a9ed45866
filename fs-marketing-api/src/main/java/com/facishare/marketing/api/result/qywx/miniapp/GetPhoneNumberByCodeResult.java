package  com.facishare.marketing.api.result.qywx.miniapp;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

@Data
public class GetPhoneNumberByCodeResult implements Serializable {
    @SerializedName("phone_info")
    private PhoneInfo phoneInfo;

    @Data
    public static class PhoneInfo implements Serializable{
        private String phoneNumber;
        private String purePhoneNumber;
        private String countryCode;
        private Watermark watermark;
    }

    @Data
    public static class Watermark implements Serializable{
        private Long timestamp;
        private String appid;
    }
}
