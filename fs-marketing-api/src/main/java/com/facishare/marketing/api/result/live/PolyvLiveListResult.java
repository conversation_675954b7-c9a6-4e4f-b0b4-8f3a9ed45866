package com.facishare.marketing.api.result.live;

import lombok.Data;

import java.io.Serializable;

@Data
public class PolyvLiveListResult implements Serializable{
    private String channelId;               //直播id
    private String name;            //直播名称
    private String watchState;       //直播状态
    private String watchUrl;          //直播地址
    private String splashImg;           //直播封面
    private Long startTime;        //直播开始时间
}
