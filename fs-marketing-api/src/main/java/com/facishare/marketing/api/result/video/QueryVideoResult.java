package com.facishare.marketing.api.result.video;

import com.facishare.marketing.api.result.MaterialTagResult;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created by z<PERSON>gh on 2020/4/29.
 */
@Data
public class QueryVideoResult implements Serializable{
    @ApiModelProperty("视频id")
    private String id;

    @ApiModelProperty("视频名称")
    private String name;

    @ApiModelProperty("视频封面")
    private String cover;

    @ApiModelProperty("时长")
    private Long duration;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("视频播放地址")
    private String url;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("创建人userId")
    private Integer userId;

    @ApiModelProperty("置顶标识")
    private boolean top;

    @ApiModelProperty("内容标签")
    private List<MaterialTagResult> materialTags;
}
