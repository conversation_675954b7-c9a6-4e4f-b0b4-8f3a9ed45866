package com.facishare.marketing.api.result.hexagon;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ListHexagonTemplateByGroupResult implements Serializable {
    @ApiModelProperty("模板id")
    private String templateId;

    @ApiModelProperty("模板名称")
    private String templateName;

    @ApiModelProperty("分组名称")
    private String groupName;

    @ApiModelProperty("分组id")
    private String groupId;

    @ApiModelProperty("模板创建者名称")
    private String creator;

    @ApiModelProperty("模板创建时间")
    private Long createTime;

    @ApiModelProperty("模板封面")
    private String coverUrl;

    @ApiModelProperty("模板封面APath")
    private String coverAPath;

    @ApiModelProperty("模板状态")
    private Integer status;

    @ApiModelProperty("H5预览二维码图片url")
    private String h5QRUrl;
    @ApiModelProperty("微信小程序二维码图片url")
    private String miniappQRUrl;
    @ApiModelProperty("百度小程序二维码图片url")
    private String bdQRUrl;
    @ApiModelProperty("百度小程序二维码图片aPath")
    private String bdQRAPath;
    private String h5QRAPath;
    private String miniappQRAPath ;
    @ApiModelProperty("可编辑")
    private boolean editable;
}
