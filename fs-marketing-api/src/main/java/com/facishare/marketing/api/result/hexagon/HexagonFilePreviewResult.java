package com.facishare.marketing.api.result.hexagon;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Auther: Smallfan
 * @Date: created in 2019-12-25 16:30
 * @Description:
**/
@Data
public class HexagonFilePreviewResult implements Serializable {
    @ApiModelProperty(value = "文件浏览链接")
    private String previewUrl;

    @ApiModelProperty(value = "文件下载链接")
    private String downloadUrl;
}
