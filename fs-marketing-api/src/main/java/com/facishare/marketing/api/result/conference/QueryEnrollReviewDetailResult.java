package com.facishare.marketing.api.result.conference;

import com.facishare.marketing.common.typehandlers.value.CustomizeFormDataEnroll;
import com.facishare.marketing.common.typehandlers.value.FieldInfoList;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by zhengh on 2019/10/23.
 */
@Data
public class QueryEnrollReviewDetailResult implements Serializable{

     @ApiModelProperty("名称")
     private String name;

     @ApiModelProperty("手机")
     private String phone;

     @ApiModelProperty("邮箱")
     private String email;

     @ApiModelProperty("活动成员id")
     private String campaignMembersObjId;

     @ApiModelProperty("创建时间")
     private Long createTime;

     @ApiModelProperty("绑定类型")
     private Integer bindCrmObjectType;

     @ApiModelProperty("职务")
     private String position;

     @ApiModelProperty("企业名称")
     private String companyName;

     @ApiModelProperty("负责人")
     private String owner;

     @ApiModelProperty("提交内容")
     private CustomizeFormDataEnroll submitContent;

     @ApiModelProperty("表单体")
     private FieldInfoList formBodySetting;
}
