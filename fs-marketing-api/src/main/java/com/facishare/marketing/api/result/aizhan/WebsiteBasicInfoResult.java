package com.facishare.marketing.api.result.aizhan;

import com.facishare.marketing.common.util.TextUtil;
import lombok.Data;

import java.io.Serializable;

@Data
public class WebsiteBasicInfoResult implements Serializable {

//    网站流量	总PV 765,779,232环比-34%	总UV 765,779,232环比-34%
//    设备流量	PC端占比:65%	移动端占比:34%
//    备类信息	备案号:浙b3-23214 性质:企业名称:杭州阿里巴巴有限责任公司 审核时间:20203-12-23


    private SeoDataInfo pv;

    private SeoDataInfo uv;

    private SeoDataInfo search;

    private String pcTraffic;

    private String mobileTraffic;

    private String recordInformation;

    private Long lastUpdateTime;


    @Data
    public static class SeoDataInfo implements Serializable {

        private String count;

        //环比
        private String growthRate;

        public SeoDataInfo(Integer count, String growthRate) {

            this.count = count+"";

            this.growthRate =growthRate;
        }

        public SeoDataInfo() {

        }
    }

}
