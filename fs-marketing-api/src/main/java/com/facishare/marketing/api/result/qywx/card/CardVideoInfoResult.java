package com.facishare.marketing.api.result.qywx.card;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * Created  By zhoux 2020/01/14
 **/
@Data
public class CardVideoInfoResult implements Serializable {

    @ApiModelProperty(value = "视频类型：1转码视频，2url视频")
    private int videoType;

    @ApiModelProperty(value = "视频的token")
    private String kmToken;

    @ApiModelProperty(value = "低清视频的url")
    private String ldUrl;

    @ApiModelProperty(value = "高清视频的url")
    private String hdUrl;

    @ApiModelProperty(value = "视频的h5封面")
    private String imageH5;

    @ApiModelProperty(value = "视频的web封面")
    private String imageWeb;

    @ApiModelProperty(value = "视频转码状态：0转码中，1上传成功，2转码失败, 4已删除")
    private Integer status;

}
