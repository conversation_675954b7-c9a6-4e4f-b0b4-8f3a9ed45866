package com.facishare.marketing.api.result.hexagon;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class GetContentCenterInfoResult implements Serializable{
    @ApiModelProperty("开启内容中心设置状态 0为关闭 1为开启")
    private Integer openContentCenter;

    @ApiModelProperty("站点id (若为空则不存内容中心)")
    private String siteId;

    @ApiModelProperty("创建者")
    private String createName;

    @ApiModelProperty("创建时间")
    private Long createTime;
}
