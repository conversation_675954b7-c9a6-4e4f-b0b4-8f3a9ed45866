package com.facishare.marketing.api.result.conference;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class GetEnrollTimeResult implements Serializable {

    //报名截止时间
    @ApiModelProperty(value = "报名截止时间")
    private String enrollEndTime;

    // 是否报名截止
    @ApiModelProperty(value = "是否报名截止")
    private Boolean enrollEnd;

    //报名截止提示信息
    @ApiModelProperty(value = "报名截止提示信息")
    private String enrollTips;
}
