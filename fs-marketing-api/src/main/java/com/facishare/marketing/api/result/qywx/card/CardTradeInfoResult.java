package com.facishare.marketing.api.result.qywx.card;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * Created  By zhoux 2020/01/14
 **/
@Data
public class CardTradeInfoResult implements Serializable {

    @ApiModelProperty(value = "一级行业编码")
    private String tradeCodeOne;
    @ApiModelProperty(value = "一级行业名称")
    private String tradeNameOne;
    @ApiModelProperty(value = "二级行业编码")
    private String tradeCodeTwo;
    @ApiModelProperty(value = "二级行业名称")
    private String tradeNameTwo;

}
