package com.facishare.marketing.api.result.moment;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryMostPopularMaterialListUnitResult implements Serializable {

    @ApiModelProperty("朋友圈海报ID")
    private String id;

    @ApiModelProperty("朋友圈海报背景图")
    private String bgUrl;

    @ApiModelProperty("朋友圈海报背景图缩略图")
    private String bgThumbnailUrl;

    @ApiModelProperty("使用人数")
    private Integer usingCount;

    @ApiModelProperty("头像列表（最多3个）")
    private List<String> avatarList;
}
