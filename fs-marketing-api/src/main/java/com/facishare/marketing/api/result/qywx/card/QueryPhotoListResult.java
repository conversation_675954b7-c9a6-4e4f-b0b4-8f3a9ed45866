package com.facishare.marketing.api.result.qywx.card;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryPhotoListResult implements Serializable {

    @ApiModelProperty(value = "名片图片列表")
    private List<PhotoListUnitResult> photoList;

}
