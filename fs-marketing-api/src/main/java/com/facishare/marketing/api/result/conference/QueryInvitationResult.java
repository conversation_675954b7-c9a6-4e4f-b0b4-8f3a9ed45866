package com.facishare.marketing.api.result.conference;

import com.facishare.marketing.common.typehandlers.value.ButtonStyle;
import com.facishare.marketing.common.typehandlers.value.FieldValueList;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by ranluch on 2019/7/24.
 */
@Data
public class QueryInvitationResult implements Serializable {
    @ApiModelProperty("邀请函id")
    private String id;

    @ApiModelProperty("会议id")
    private String conferenceId;

    @ApiModelProperty("邀请函名称")
    private String invitationName;

    @ApiModelProperty("邀请函详情")
    private FieldValueList invitationDetail;

    @ApiModelProperty("邀请函按钮样式")
    private ButtonStyle invitationButton;

    @ApiModelProperty("创建时间")
    private Long createTime;

    @ApiModelProperty("关联表单id")
    private String formId;

    @ApiModelProperty("封面图片url")
    private String coverImageUrl;

    @ApiModelProperty("封面缩略图url")
    private String coverImageThumbUrl;

    @ApiModelProperty("会议开始时间")
    private Long startTime;

    @ApiModelProperty("会议结束时间")
    private Long endTime;

    @ApiModelProperty("会议报名截止时间")
    private Long enrollEndTime;

    @ApiModelProperty("活动状态 1 有效 2 失效 3 删除")
    private Integer activityStatus;

}
