package com.facishare.marketing.api.result.moment;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryEnterpriseMaterialListUnitResult implements Serializable {

    @ApiModelProperty("素材id")
    private String id;

    @ApiModelProperty("原图url")
    private String bgUrl;

    @ApiModelProperty("缩略图url")
    private String bgThumbnailUrl;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("创建时间")
    private Long createTime;

}
