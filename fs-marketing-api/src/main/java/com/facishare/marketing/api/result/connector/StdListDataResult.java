package com.facishare.marketing.api.result.connector;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

@Data
@ToString
public class StdListDataResult implements Serializable {
    private List<StdDataResult> dataList = new ArrayList<>();
    /**
     * 下次查询开始Id，非必填
     */
    private String maxId;
    /**
     * 下次查询开始时间，非必填
     */
    private Long maxTime;

    @Data
    public static class StdDataResult implements Serializable{
        /**
         * 对象ApiName
         */
        private String objAPIName;

        /**
         * 主对象数据
         */
        private ObjectData masterFieldVal;
    }

    @Data
    public static class ObjectData extends LinkedHashMap<String, Object> implements Serializable {

    }
}
