package com.facishare.marketing.api.result.crowd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("目标人群运营计划详情")
public class MarketingCrowdPlanStatisticsResult implements Serializable {

    @ApiModelProperty(value = "人群人数")
    private int marketingGroupUserNum;
    @ApiModelProperty(value = "访问人数")
    private int uv;
    @ApiModelProperty(value = "线索数")
    private int leadNum;

}
