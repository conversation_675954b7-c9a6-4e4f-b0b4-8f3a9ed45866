package com.facishare.marketing.api.result.advertiser;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class AdBigScreenSettingDetailResult implements Serializable {

    // 获客成本
    private Setting customerAcquisitionCost;

    // 广告投放效果趋势
    private Setting launchEffectTrend;

    // 广告获客转化漏斗
    private Setting acquisitionCustomerConvertFunnel;

    // 广告账户获客对比
    private Setting accountAcquisitionCustomerCompare;

    // 广告账户投入产出分析
    private Setting accountInputOutputAnalysis;

    private Setting overView;

    private Setting leadsArealDistributions;

    // 转化周期设置
    private Setting convertPeriod;

    // 时间设置
    private TimeSetting timeSetting;

    @ApiModelProperty("位置，按顺序排列")
    private List<String> modulePositions;

    @Data
    public static class Setting implements Serializable {

        private String name;

        private List<Field> fieldList;

        private String position;
    }

    @Data
    public static class Field implements Serializable {

        private String id;

        private String name;

        private boolean canEdit = false;

        private boolean hidden = false;

        private String type;

        private List<FieldValue> fieldValueList;
    }

    @Data
    public static class FieldValue implements Serializable {

        // 字段的值
        private String value;

        // value的展示值
        private String label;

        // 是否被选中
        private Boolean selected;
    }

    @Data
    public static class TimeSetting implements Serializable {

        private String timeRange;

        private Date beginTime;

        private Date endTime;

        private String compareTimeType;

        private Date compareBeginTime;

        private Date compareEndTime;
    }
}
