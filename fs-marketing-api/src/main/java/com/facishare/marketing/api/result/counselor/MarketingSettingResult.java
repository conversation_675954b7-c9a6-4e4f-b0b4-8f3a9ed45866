package com.facishare.marketing.api.result.counselor;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/12/20 11:50
 * @Version 1.0
 */
@Data
public class MarketingSettingResult implements Serializable {
    @ApiModelProperty("企业Ea")
    private String ea;
    @ApiModelProperty("公众号的名字")
    private String accountName;
    @ApiModelProperty("公众号的ID")
    private String appId;
    @ApiModelProperty("客服人员集合ID")
    private List<Integer> userIds;
    @ApiModelProperty("客服部门ID集合")
    private List<Integer> departmentIds;
    @ApiModelProperty("昵称")
    private String nickName;
    @ApiModelProperty("头像地址")
    private String avatarUrl;
    @ApiModelProperty("回复语")
    private String scannedReply;

}

