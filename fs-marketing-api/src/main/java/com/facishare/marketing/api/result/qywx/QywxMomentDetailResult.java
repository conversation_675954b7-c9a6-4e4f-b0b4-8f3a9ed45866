package com.facishare.marketing.api.result.qywx;


import com.facishare.marketing.api.vo.qywx.MomentMessageVO;
import com.facishare.marketing.common.typehandlers.value.TagName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 * @Description
 * @Date 11:31 2020/2/17
 * @ModifyBy
 */
@Data
public class QywxMomentDetailResult implements Serializable {
    @ApiModelProperty(value = " 消息类型 1图文， 2:图片， 3视频")
    private Integer momentType;

    @ApiModelProperty(value = "文本内容")
    private String content;

    @ApiModelProperty("发送范围  0-全部 1-指定")
    private Integer sendRange;

    @ApiModelProperty("已发表工数")
    private Integer sendEmployeeCount;

    @ApiModelProperty("未发表员工数")
    private Integer unSendEmployeeCount;

    @ApiModelProperty("已发送的客户数量")
    private Integer sendedCount;

    @ApiModelProperty("未发送人数")
    private Integer unsendedCount;

    @ApiModelProperty("线索数")
    private int clueCount;

    @ApiModelProperty("创建时间")
    private Long createTime;

    @ApiModelProperty("发送类型 1：立即发送 2：定时发送")
    private Integer sendType;

    @ApiModelProperty("定时推送时间, 仅type=2时有值")
    private Long fixedTime;

    @ApiModelProperty(value = "图片消息")
    private List<Image> image;

    @ApiModelProperty(value = "link")
    private Link link;

    @ApiModelProperty(value = "发送对象")
    private String createByName;

    @ApiModelProperty(value = "关联的物料信息")
    private List<MaterialInfo> materialInfoList;


//    @ApiModelProperty(value = "发送员工id")
//    private List<String> userIdList;
//
//    @ApiModelProperty(value = "发送客户企微标签")
//    private List<TagName> tagNameList;
//
//    @ApiModelProperty("通知发送部门")
//    private List<Integer> departmentIds;
    @Data
    public static class Image implements Serializable {
        @ApiModelProperty("图片路径")
        private String imagePath;
        @ApiModelProperty("图片名称")
        private String name;
        @ApiModelProperty("图片大小")
        private Integer size;
        @ApiModelProperty("下载地址")
        private String downLoadUrl;
    }

    @Data
    public static class Link implements Serializable {
        @ApiModelProperty("图文消息标题")
        private String title;
        @ApiModelProperty("文消息封面的TN path")
        private String picPath;
        @ApiModelProperty("封面的下载地址")
        private String picUrl;
        @ApiModelProperty("图文消息的链接")
        private String url;//图文消息的链接
        @ApiModelProperty("图文消息的链接")
        private int linkType;//图文消息类型
    }

    @Data
    public static class MaterialInfo implements Serializable {
        private String objectId;
        private Integer objectType;
    }

}
