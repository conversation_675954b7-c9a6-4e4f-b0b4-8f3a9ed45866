package com.facishare.marketing.api.result.qywx;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/26
 **/
@Data
public class QywxEmployeeResult implements Serializable {

    private String id;//企微员工对象id

    private String name; //企微员工姓名

    private String userId; //企微密文userid

    private String fxUserId; //CRM员工ID

    private String position; //职称

    private String mobile; //手机号

    private String telephone; //电话

    private String address; //地址

    private List<String> department; //所属部门id列表

    private List<String> deptOrder; //部门内的排序值

    private String mainDepartment; //企微主部门ID

    private String directLeader; //直属上级UserID列表

    private String status; //激活状态

    private List<String> appScope; //当前所在应用

    private String wechatDepartmentName; //企微员工部门名称

    private Long crmBindTime; //CRM账号绑定时间

    private List<String> crmBindType; //CRM账号绑定类型

}
