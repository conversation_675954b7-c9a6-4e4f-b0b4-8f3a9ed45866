package com.facishare.marketing.api.result.qywx.card;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class FeedCommentUnitResult implements Serializable {
    private String avatar;
    private String createBy;
    private String name;
    private String content;
    private String id;
    @ApiModelProperty(value = "评论类型：1个人动态评论，2公司动态评论")
    private Integer type;
}
