package com.facishare.marketing.api.result.kis;

import java.io.Serializable;
import lombok.Data;

/**
 * @创建人 zhengliy
 * @创建时间 2019/2/22 17:54
 * @描述
 */
@Data
public class GetEnterpriseSpreadStatisticSumUpResult implements Serializable {
    private Integer spreadCount = 0;  // 员工推广次数
    private Integer spreadUserCount = 0; //员工推广人数
    private Integer forwardCount = 0; // 转发次数，不含员工
    private Integer lookUpCount = 0; // 访问次数，不含员工
//    private Integer forwardUserCount = 0; // 转发人数，不含员工
//    private Integer lookUpUserCount = 0; // 访问人数，不含员工
    private Integer leadAccumulationCount = 0; // 线索累积量
//    private Integer customerAccumulationCount = 0; // 客户累积量

    private Integer countDepartment = 0;  //部门数
    private Integer countUser = 0;       // 同事数

    private String coverUrl;  // 物料封面

    private String title;   //物料标题

    private Integer objectType;
    private String objectId;
}
