package com.facishare.marketing.api.data.usermarketingaccounttag;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 25/03/2019
 */
@Data
public class TagData implements Serializable {
    @ApiModelProperty(value = "标签id", required = true)
    private String tagId;
    @ApiModelProperty(value = "标签名称", required = true)
    private String tagName;
}
