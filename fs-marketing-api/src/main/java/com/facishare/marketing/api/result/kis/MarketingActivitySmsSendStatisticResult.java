package com.facishare.marketing.api.result.kis;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.ToString;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @创建人 zhengliy
 * @创建时间 2019/2/28 15:06
 * @描述
 */
@Data
@ToString
public class MarketingActivitySmsSendStatisticResult implements Serializable {
    private static final long serialVersionUID = 155698789429601555L;
    @ApiModelProperty(value = "实际需要发送人数")
    Integer needSend = 0;
    @ApiModelProperty(value = "失败发送人数")
    Integer failedSend = 0;
    @ApiModelProperty(value = "成功发送人数")
    Integer actualSend = 0;

    @ApiModelProperty(value = " 1导入excel表格，2按选择人群")
    Integer sendRange;

    @ApiModelProperty("人群title 只有sendRange==2才有效")
    List<String> marketingUserGroupTitle;

    @ApiModelProperty("短信内容")
    private String smsContent;

    @ApiModelProperty("短信模板名")
    private String templateName;
}
