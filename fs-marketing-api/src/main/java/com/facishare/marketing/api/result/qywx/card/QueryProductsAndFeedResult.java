package com.facishare.marketing.api.result.qywx.card;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryProductsAndFeedResult implements Serializable {

    @ApiModelProperty(value = "产品列表")
    private List<ProductListUnitResult> productList;

    @ApiModelProperty(value = "7天内最新一条动态")
    private FeedDetailResult lastFeed;
}
