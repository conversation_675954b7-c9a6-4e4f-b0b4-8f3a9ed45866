package com.facishare.marketing.api.result.qywx.card;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ProductListUnitResult implements Serializable {

    @ApiModelProperty(value = "产品id", notes = "产品id")
    private String id;

    @ApiModelProperty(value = "产品名称", notes = "产品名称")
    private String name;

    @ApiModelProperty(value = "产品简介", notes = "产品简介")
    private String summary;

    @ApiModelProperty(value = "产品头图", notes = "产品头图")
    private String productPic;

    @ApiModelProperty(value = "产品头图缩略图", notes = "产品头图缩略图")
    private String productThumbPic;

    @ApiModelProperty(value = "产品原价", notes = "产品原价")
    private String price;

    @ApiModelProperty(value = "产品折扣价", notes = "产品折扣价")
    private String discountPrice;

}
