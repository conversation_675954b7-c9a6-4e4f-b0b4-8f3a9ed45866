package com.facishare.marketing.api.result.kis;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName GetDailyHeadPosterInfoResult
 * @Description
 * <AUTHOR>
 * @Date 2019/2/26 4:14 PM
 */
@Data
public class GetDailyHeadPosterInfoResult implements Serializable {
    private List<GetDailyHeadPosterBaseInfoResult>baseInfoList;
    private String name;
    private String vocation;
    private String avatar;
    private String qrUrl;
    private String cardUid;
    private String coverUrl;
    private String cardId;
}
