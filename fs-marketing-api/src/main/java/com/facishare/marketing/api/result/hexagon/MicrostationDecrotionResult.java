package com.facishare.marketing.api.result.hexagon;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class MicrostationDecrotionResult implements Serializable{
    @ApiModelProperty("微站模板分类")
    private String templateClassfy;

    @ApiModelProperty("模板名称")
    private String templateName;


    @ApiModelProperty("模板创建时间")
    private Long createTime;

    @ApiModelProperty("模板封面")
    private String coverUrl;

    @ApiModelProperty("模板封面APath")
    private String coverAPath;
}
