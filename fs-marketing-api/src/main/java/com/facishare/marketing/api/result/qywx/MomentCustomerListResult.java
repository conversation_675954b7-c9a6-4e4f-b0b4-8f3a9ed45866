package com.facishare.marketing.api.result.qywx;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/24 17:02
 */
@Data
public class MomentCustomerListResult implements Serializable {

    @ApiModelProperty("企微客户对象id")
    private String id;

    @ApiModelProperty("企微外部联系人id")
    private String externalUserId;

    @ApiModelProperty("头像")
    private String avatar;

    @ApiModelProperty("客户姓名")
    private String customerName;

    @ApiModelProperty("微信昵称")
    private String nickName;

    @ApiModelProperty("员工姓名")
    private String employeeName;
}
