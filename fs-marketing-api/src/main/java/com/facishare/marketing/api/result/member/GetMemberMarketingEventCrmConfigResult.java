package com.facishare.marketing.api.result.member;

import com.facishare.marketing.common.typehandlers.value.FieldMappings;
import com.facishare.marketing.common.typehandlers.value.TagNameList;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * Created  By zhoux 2020/12/28
 **/
@Data
public class GetMemberMarketingEventCrmConfigResult implements Serializable {

    @ApiModelProperty("数据id")
    private String id;

    @ApiModelProperty("市场活动id")
    private String marketingEventId;

    @ApiModelProperty("crm映射")
    private FieldMappings memberToLeadFieldMappings;

    @ApiModelProperty("线索池id")
    private String leadPoolId;

    @ApiModelProperty("业务类型")
    private String leadRecordType;

    @ApiModelProperty("标签Name列表")
    private TagNameList tagNameList;

    @ApiModelProperty("跳转报名物料id")
    private String jumpObjectId;

    @ApiModelProperty("跳转报名物料类型")
    private Integer jumpObjectType;

    @ApiModelProperty("跳转报名地址")
    private String jumpUrl;

    @ApiModelProperty("报名截止时间")
    private String enrollTime;

    @ApiModelProperty("是否开启报名截止时间  0:未开启 1:开启")
    private Integer status;

    @ApiModelProperty("截止提示")
    private String enrollTip;
}
