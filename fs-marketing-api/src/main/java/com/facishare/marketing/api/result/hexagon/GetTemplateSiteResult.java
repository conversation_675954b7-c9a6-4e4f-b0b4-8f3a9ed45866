package com.facishare.marketing.api.result.hexagon;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

import java.io.Serializable;
@Data
public class GetTemplateSiteResult implements Serializable {
    @ApiModelProperty("模板站点ID")
    private String id;

    @ApiModelProperty("站点名称")
    private String name;

    @ApiModelProperty("封面apath")
    private String coverUrl;

    @ApiModelProperty("行业类别")
    private Integer category;

    @ApiModelProperty("模板的次序")
    private Integer index;


}
