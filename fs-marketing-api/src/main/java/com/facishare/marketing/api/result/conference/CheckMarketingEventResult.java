package com.facishare.marketing.api.result.conference;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * Created  By zhoux 2019/07/30
 **/
@Data
public class CheckMarketingEventResult implements Serializable {

    @ApiModelProperty("是否绑定会议")
    private boolean bindConference;

    @ApiModelProperty("绑定会议id")
    private String bindConferenceId;

    @ApiModelProperty("市场活动类型")
    private String marketingEventType;

    @ApiModelProperty("会议标题")
    private String bindConferenceTitle;

}
