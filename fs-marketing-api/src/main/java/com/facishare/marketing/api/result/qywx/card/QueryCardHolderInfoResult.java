package com.facishare.marketing.api.result.qywx.card;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * Created  By zhoux 2020/12/23
 **/
@Data
public class QueryCardHolderInfoResult implements Serializable {

    @ApiModelProperty(value = "名片id")
    private String id;

    @ApiModelProperty(value = "名片所有者uid")
    private String uid;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "职位")
    private String vocation;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "邮箱地址")
    private String email;

    @ApiModelProperty(value = "头像")
    private String avatar;

    @ApiModelProperty(value = "性别", notes = "性别: 1男2女")
    private Integer gender;

    @ApiModelProperty(value = "隐私配置状态 1 可见 2 不可见 为空为全部可见")
    private String visualRange;

}
