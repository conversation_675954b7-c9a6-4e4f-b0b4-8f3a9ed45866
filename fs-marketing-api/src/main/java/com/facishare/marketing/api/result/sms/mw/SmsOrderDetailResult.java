package com.facishare.marketing.api.result.sms.mw;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * Created  By zhoux 2020/05/20
 **/
@Data
public class SmsOrderDetailResult implements Serializable {

    // 主键 uuid
    private String id;
    // 企业ea
    private String ea;
    // 订单创建者的userId
    private Integer userId;
    // 订单创建者的名字
    private String creator;
    // 价格
    private float price;
    // 购买金额
    private float paymentCount;
    // 购买短信条数
    private Integer purchaseCount;
    // 订单状态 (0 已支付 1 未支付)
    private int status;
    // 购买份数
    private Integer copies;
    // crm订单id
    private String crmOrderId;
    // 订单套餐
    private String orderPackage;
    // 订单来源(0:应用内购买  1:CRM下单)
    private Integer orderSource;
    private Date createTime;
    private Date updateTime;

}
