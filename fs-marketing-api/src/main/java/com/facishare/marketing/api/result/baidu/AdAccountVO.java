package com.facishare.marketing.api.result.baidu;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("广告账户VO")
public class AdAccountVO implements Serializable {

    @ApiModelProperty("广告账户ID")
    private String id;

    @ApiModelProperty("广告账户名字")
    private String name;

    @ApiModelProperty("广告账户平台类型")
    private String source;

    @ApiModelProperty("-2:同步数据失败 0:正常 1:已停用 2:同步数据中 3:广告数据未同步")
    private Integer status;
}
