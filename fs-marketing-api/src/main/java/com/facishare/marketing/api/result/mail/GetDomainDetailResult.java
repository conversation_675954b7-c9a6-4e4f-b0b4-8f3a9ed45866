package com.facishare.marketing.api.result.mail;

import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * Created  By zhoux 2020/07/02
 **/
@Data
public class GetDomainDetailResult implements Serializable {

    @ApiModelProperty("域名名称")
    private String name;

    @ApiModelProperty("域名类型")
    private String type;

    @ApiModelProperty("域名验证值")
    private Integer verify;

    @ApiModelProperty("此域名 SPF 的主机记录")
    private String spfDomain;

    @ApiModelProperty("此域名 SPF 的配置值")
    private String spfValue;

    @ApiModelProperty("此域名 DKIM 的主机记录")
    private String dkimDomain;

    @ApiModelProperty("此域名 DKIM 的配置值")
    private String dkimValue;

    @ApiModelProperty("此域名 MX 的主机记录")
    private String mxDomain;

    @ApiModelProperty("此域名 MX 的配置值")
    private String mxValue;

    @ApiModelProperty("此域名 Dmarc 的主机记录")
    private String dmarcDomain;

    @ApiModelProperty("此域名 Dmarc 的配置值")
    private String dmarcValue;

}
