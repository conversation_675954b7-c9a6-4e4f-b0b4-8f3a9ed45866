package com.facishare.marketing.api.result.conference;

import com.facishare.marketing.api.result.MaterialTagResult;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by z<PERSON><PERSON> on 2019/7/17.
 */
@Data
public class QueryConferenceListResult implements Serializable{

    @ApiModelProperty("市场活动id")
    private String marketingEventId;    //市场活动id

    @ApiModelProperty("会议id")
    private String conferenceId;        //会议id

    @ApiModelProperty("标题")
    private String title;               //标题

    @ApiModelProperty("会议状态 1 有效 2 失效 3 删除")
    private Integer status;             //会议状态

    @ApiModelProperty("会议进行状态 2 会议未开始 3 会议进行中 4 会议已结束")
    private Integer flowStatus;        // 会议进行状态  ConferenceTimeFlowStatusEnum

    @ApiModelProperty("会议开始时间")
    private Long startTime;           //会议开始时间

    @ApiModelProperty("会议截至时间")
    private Long endTime;             //会议截至时间

    @ApiModelProperty("签到人数")
    private int signInCount;            //签到人数

    @ApiModelProperty("报名人数")
    private int enrollCount;            //报名人数

    @ApiModelProperty("封面图apath")
    private String aPath;                 //封面图url

    @ApiModelProperty("封面图url")
    private String url;                 //封面图url

    @ApiModelProperty("封面缩略图url")
    private String thumbnailUrl;        //封面缩略图url

    @ApiModelProperty("封面缩略图Apath")
    private String thumbnailAPath;        //封面缩略图url

    @ApiModelProperty("会议形式：1线下会议，2线上会议")
    private Integer type;

    @ApiModelProperty("会议地点")
    private String location;    //会议地点

    @ApiModelProperty("挂接营销活动标题（app/H5 推广列表）")
    private String marketingActivityTitle;

    @ApiModelProperty("挂接营销活动id（app/H5 推广列表）")
    private String marketingActivityId;

    @ApiModelProperty("挂接营销活动总数（app/H5 推广列表）")
    private Integer marketingActivityCount = 0;

    @ApiModelProperty("表单id")
    private String formId;

    @ApiModelProperty("会议详情站点id")
    private String activityDetailSiteId;

    @ApiModelProperty("是否展示在内容中心和app会议推广里面")
    private Boolean showActivityList;

    @ApiModelProperty("分享朋友圈封面裁剪图url")
    private String sharePicMiniAppCutUrl;

    @ApiModelProperty("分享朋友圈封面裁剪图url")
    private String sharePicH5CutUrl;

    @ApiModelProperty("普通推广微页面分享与封面裁剪图url")
    private String sharePicOrdinaryCutUrl;

    @ApiModelProperty("原图封面url")
    private String sharePicOrdinaryUrl;

    @ApiModelProperty(value = "生命状态")
    private String lifeStatus;

    @ApiModelProperty("内容标签")
    private List<MaterialTagResult> materialTags;

    private String shareDesc;

    private String shareTitle;
}
