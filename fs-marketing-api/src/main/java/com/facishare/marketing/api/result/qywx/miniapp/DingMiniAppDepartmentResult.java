package com.facishare.marketing.api.result.qywx.miniapp;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/25 14:23
 */
@Data
public class DingMiniAppDepartmentResult implements Serializable {

    /**
     * 部门ID
     */
    @SerializedName("dept_id")
    private Integer deptId;

    /**
     * 部门名称
     */
    @SerializedName("name")
    private String name;

    /**
     * 父部门ID
     */
    @SerializedName("parent_id")
    private Integer parentId;

    /**
     * 是否同步创建一个关联此部门的企业群
     */
    @SerializedName("create_dept_group")
    private Boolean createDeptGroup;

    /**
     * 部门群已经创建后，有新人加入部门是否会自动加入该群
     */
    @SerializedName("auto_add_user")
    private Boolean autoAddUser;
}
