package com.facishare.marketing.api.result.kis;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * Created by z<PERSON>gh on 2019/2/22.
 */
@Data
@ToString
public class QuerySpreadTaskResult implements Serializable{
    private String spreadTaskId;

    private String marketingActivityId;

    private String spreadTitle;

    private String targetId;

    private String targetName;

    private String activityDetailSiteId;

    private Integer targetType;

    private Integer status;

    private String coverUrl;

    private Long createTimestamp;

    private String createTime;

    private String startTime;

    private String endTime;

    private Long startTimeStamp;

    private Long endTimeStamp;

    //宣传语
    private String description;

    private String shareTitle;
    private String shareDesc;

    @ApiModelProperty("分享朋友圈封面裁剪图url")
    private String sharePicMiniAppCutUrl;

    @ApiModelProperty("分享朋友圈封面裁剪图url")
    private String sharePicH5CutUrl;

    @ApiModelProperty("普通推广微页面分享与封面裁剪图url")
    private String sharePicOrdinaryCutUrl;

    @ApiModelProperty("原图封面url")
    private String sharePicOrdinaryUrl;

    @ApiModelProperty("是否为多物料营销活动")
    private Boolean multipleMaterial;

    @ApiModelProperty(value = "海报图片推广展示,0关闭,1打开")
    private Integer staffInfoShow;
}
