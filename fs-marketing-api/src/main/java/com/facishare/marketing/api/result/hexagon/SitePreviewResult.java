package com.facishare.marketing.api.result.hexagon;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SitePreviewResult implements Serializable {
    @ApiModelProperty("H5预览二维码图片url")
    private String h5QRUrl;

    @ApiModelProperty("微信小程序二维码图片url")
    private String miniappQRUrl;

    @ApiModelProperty("百度小程序二维码图片url")
    private String bdQRUrl;
    @ApiModelProperty("百度小程序二维码图片aPath")
    private String bdQRAPath;
    private String h5QRAPath;
    private String miniappQRAPath ;

}
