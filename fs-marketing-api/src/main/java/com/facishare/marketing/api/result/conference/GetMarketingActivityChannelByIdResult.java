package com.facishare.marketing.api.result.conference;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * Created  By zhoux 2021/07/09
 **/
@Data
@AllArgsConstructor
public class GetMarketingActivityChannelByIdResult implements Serializable {

    @ApiModelProperty("渠道值 目前支持 短信与邮件")
    private String channel;

}
