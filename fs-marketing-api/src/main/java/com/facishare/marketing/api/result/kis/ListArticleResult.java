package com.facishare.marketing.api.result.kis;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.facishare.marketing.api.result.MaterialTagResult;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @创建人 zhengliy
 * @创建时间 2019/3/13 18:06
 * @描述
 */
@Data
public class ListArticleResult implements Serializable {
    private String id;
    private String uid;
    // 封面
    private String photoId;
    private String photoUrl;
    private String photoThumbnailUrl;
    // 标题
    private String title;
    // 摘要
    private String summary;
    // 文章路径
    private String articlePath;
    // 文章类型 1:原创 2:转载
    private Integer articleType;
    // 作者
    private String creator;
    // 来源
    private String source;
    // 来源类型 1:公众号
    private Integer sourceType;
    // 状态 -1:未启用 1:已启用 4:已停用
    private Integer status;
    // 文章图片和纷享图片的对应关系json串
    //private String pictureJson;
    // 纷享企业账号
    private String ea;
    // 纷享个人账号
    private Integer userId;
    // 文章链接
    private String url;
    // 创建时间
    private Date createTime;
    // 更新时间
    private Date lastModifyTime;
    // 创建时间
    private Long createTimeStamp;
    // 更新时间
    private Long lastModifyTimeStamp;
    // 文章所属 1:个人 2:公司
    private Integer belong;
    // 推荐语
    private String recommendation;
    // 个人是否已选用公司文章
    private boolean isChoose;
    private String feedId;

    // 自定义文章头部
    private String preArticleContent;

    // 营销活动（推广内容）
    private String marketingActivityTitle;

    // 营销活动id
    private String marketingActivityId;

    private Integer marketingActivityCount = 0;

    // 分享图片(压缩图)
    private String shareImg;

    // 推广任务id
    private String spreadTaskId;

    private boolean top;

    @ApiModelProperty("分享朋友圈封面裁剪图url")
    private String sharePicMiniAppCutUrl;

    @ApiModelProperty("分享朋友圈封面裁剪图url")
    private String sharePicH5CutUrl;

    @ApiModelProperty("普通推广微页面分享与封面裁剪图url")
    private String sharePicOrdinaryCutUrl;

    @ApiModelProperty("原图封面url")
    private String sharePicOrdinaryUrl;

    @ApiModelProperty("内容标签")
    private List<MaterialTagResult> materialTags;
}
