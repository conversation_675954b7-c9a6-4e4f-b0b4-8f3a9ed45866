package com.facishare.marketing.api.result.baidu;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by ranluch on 2019/11/27.
 */
@Data
public class QueryAccountInfoResult implements Serializable {
    @ApiModelProperty("营销通定义的账户id")
    private String id;

    @ApiModelProperty("ea")
    private String ea;

    @ApiModelProperty("营销通定义的账户状态  0:正常 1:停止使用 2:同步数据中 ")
    private Integer status;

    @ApiModelProperty("账户详情")
    private AccountInfo accountInfo;

    @ApiModelProperty("账户状态数据同步状态")
    private Integer refreshStatus;

    @ApiModelProperty("最后一次成功同步的时间")
    private Long refreshSuccessTime;
    @ApiModelProperty("最后一次同步的时间")
    private Long refreshTime;

    @Data
    public static class AccountInfo implements Serializable {
        @ApiModelProperty("账户id")
        private Long accountId;
        @ApiModelProperty("账户名称")
        private String username;
        @ApiModelProperty("账户余额")
        private Double balance;
        @ApiModelProperty("账户累计消费")
        private Double cost;
        @ApiModelProperty("账户预算")
        private Double budget;
        @ApiModelProperty("账户预算类型：0不设置预算，1为日预算，2为周预算")
        private Integer budgetType;
        @ApiModelProperty("账户状态")
        private Integer userStat;
        @ApiModelProperty("账户类型")
        private Integer type;

        @ApiModelProperty("授权的用户名称")
        private String authUserName;
    }
}
