package com.facishare.marketing.api.result.open.material;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "物料详情结果")
public class MaterialDetailResult implements Serializable {

    @ApiModelProperty(value = "产品详情,materialType = 1才会有值")
    private List<ProductDetailResult> productDetailResultList;
}
