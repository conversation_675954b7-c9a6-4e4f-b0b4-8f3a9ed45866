package com.facishare.marketing.api.result.mail;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * Created  By zhoux 2020/07/03
 **/
@Data
public class QuerySendReplyDataResult implements Serializable {

    @ApiModelProperty("数据id")
    private String id;

    @ApiModelProperty("数据类型 0 发信人 1 接收人")
    private Integer type;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("数据状态(0 正常 1 作废 99 删除)")
    private Integer status;

    @ApiModelProperty("是否为默认值")
    private Boolean defaultValue;

    @ApiModelProperty("创建时间")
    private Long createTime;

    @ApiModelProperty("更新时间")
    private Long updateTime;

}
