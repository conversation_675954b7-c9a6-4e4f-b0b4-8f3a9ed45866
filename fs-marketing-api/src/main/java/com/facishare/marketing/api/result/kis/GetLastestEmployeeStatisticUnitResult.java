package com.facishare.marketing.api.result.kis;

import com.facishare.marketing.api.result.BoardCardResult;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName GetLastestEmployeeStatisticUnitResult
 * @Description
 * <AUTHOR>
 * @Date 2019/2/28 4:27 PM
 */
@Data
public class GetLastestEmployeeStatisticUnitResult implements Serializable {
    private Integer objectType;
    private String objectId;
    private String title;
    private String coverUrl;
    private String objectTitle;

    private Integer lookUpCount;
    private Integer forwardCount;
    private Integer leadAccumulationCount;
    private Long spreadTime;

    private Long activityStartTime;
    private Long activityEndTime;

    private String cardCompanyName;

    private Integer spreadCount;
    private Integer messageUserCount;

    private String marketingActivityId;

    private Integer spreadType;

    /*@ApiModelProperty("看板名称")
    private String boardCardName;

    @ApiModelProperty("看板类型 marketing_activity_data 营销活动数据卡片 external_user_data 私域流量池数据卡片")
    private Integer boardCardType;

    @ApiModelProperty("看板id")
    private String boardCardId;

    @ApiModelProperty("看板数据类型 uv访问人数， pv访问次数，form_data_user_count表单用户数， employee_spread_count员工推广次数")
    private String boardCardGoalType;

    @ApiModelProperty("目标值")
    private Integer boardCardGoalValue;*/

    @ApiModelProperty("1 推广  2 看板")
    private Integer dataType;

    @ApiModelProperty("看板详情")
    private BoardCardResult boardCardResult;

    @ApiModelProperty("会议详情站点id")
    private String activityDetailSiteId;

    @ApiModelProperty("访问人数")
    private int lookUpUserCount;

    @ApiModelProperty("转发人数")
    private int forwardUserCount;
}
