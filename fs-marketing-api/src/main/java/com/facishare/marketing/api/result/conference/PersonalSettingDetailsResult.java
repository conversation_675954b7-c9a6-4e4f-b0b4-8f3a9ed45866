package com.facishare.marketing.api.result.conference;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * Created  By zhoux 2020/10/30
 **/
@Data
public class PersonalSettingDetailsResult implements Serializable {

    @ApiModelProperty("是否展示参会人员")
    private Boolean showParticipants;

    @ApiModelProperty("是否展示待审核人员")
    private Boolean showPendingUser;

    @ApiModelProperty("是否展示验票")
    private Boolean showCheckTicket;

}
