package com.facishare.marketing.api.result.calendar;

import com.facishare.marketing.api.data.MarketingEventData;
import com.facishare.marketing.api.data.MarketingUserGroupData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019/7/12.
 */
@Data
@ToString
public class MarketingEventsResult implements Serializable {

    @ApiModelProperty(value = "市场活动")
    private MarketingEventData marketingEvent;
    @ApiModelProperty(value = "人群列表")
    private List<MarketingUserGroupData> marketingUserGroups;
    @ApiModelProperty(value = "来源为全员营销的线索数")
    private Integer noticeLeadNum;
    @ApiModelProperty(value = "来源为短信的线索数")
    private Integer smsLeadNum;
    @ApiModelProperty(value = "来源为微联服务号的线索数")
    private Integer weChatServiceLeadNum;
    @ApiModelProperty(value = "来源为h5的线索数")
    private Integer h5LeadNum;
    @ApiModelProperty(value = "来源为客脉的线索数")
    private Integer mankeepLeadNum;
    @ApiModelProperty(value = "来源为企业微信线索数")
    private Integer qywxLeadNum;
    @ApiModelProperty(value = "线索数")
    private Integer leadNum;
    @ApiModelProperty(value = "访问人数")
    private Integer uv;
    @ApiModelProperty(value = "访问次数")
    private Integer pv;
    @ApiModelProperty(value = "企业推广次数")
    private Integer enterpriseSpreadNum;
    @ApiModelProperty(value = "统计crm市场活动下的线索总数")
    private Integer totalNum;

    private String eventForm;

    public void sumUpLeadCount(){
        leadNum = zeroIfNull(mankeepLeadNum) + zeroIfNull(noticeLeadNum) + zeroIfNull(smsLeadNum) + zeroIfNull(weChatServiceLeadNum) + zeroIfNull(h5LeadNum);
    }

    public int zeroIfNull(Integer num){
        return num == null ? 0 : num;
    }
}
