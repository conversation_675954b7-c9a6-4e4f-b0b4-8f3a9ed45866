package com.facishare.marketing.api.arg.customizedSpread;

import com.facishare.marketing.api.vo.BaseVO;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class DeleteCustomizedSpreadChannelArg extends BaseVO {

    private String id; // 自定义群发通道id

    @Override
    public Result checkParam() {
        if (StringUtils.isBlank(id)) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return super.checkParam();
    }
}
