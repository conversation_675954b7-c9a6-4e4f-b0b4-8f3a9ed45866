package com.facishare.marketing.api.result.conference;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * Created  By zhoux 2020/12/29
 **/
@Data
public class QueryAllCampaignDataResult implements Serializable {

    @ApiModelProperty("参会人员id")
    private String id;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("手机")
    private String phone;

}
