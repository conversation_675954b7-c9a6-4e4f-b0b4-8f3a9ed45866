package com.facishare.marketing.api.result.qywx.privateMessage;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * Created  By zhoux 2020/10/09
 **/
@Data
public class PrivateMessageResult implements Serializable {

    @ApiModelProperty(value = "消息id")
    private String id;

    @ApiModelProperty(value = "消息内容")
    private String content;

    /**
     * 消息类型  com.facishare.mankeep.common.enums.PrivateMessageTypeEnum
     */
    @ApiModelProperty(value = "消息类型")
    private Integer type;

    @ApiModelProperty(value = "发送方用户id")
    private String sendUid;

    @ApiModelProperty(value = "用户姓名")
    private String name;

    @ApiModelProperty(value = "用户头像地址")
    private String headUrl;

    @ApiModelProperty(value = "图片原图地址(图片信息)")
    private String picUrl;

    @ApiModelProperty(value = "图片缩略图地址(图片信息)")
    private String picThumbnailUrl;

    @ApiModelProperty(value = "创建时间")
    private Long createTime;

    @ApiModelProperty(value = "文件信息")
    private FileInfo fileInfo;

    @ApiModelProperty(value = "产品信息")
    private ProductInfo productInfo;

    @ApiModelProperty(value = "对方Customer信息主键(本人信息时为空)")
    private String customerId;

    /**
     * 客脉类型： com.facishare.mankeep.common.enums.CustomerCTypeEnum
     */
    @ApiModelProperty(value = "对方的客脉类型(本人信息时为空)")
    private Integer cType;

    @ApiModelProperty(value = "个人公众号信息")
    private WxOfficialAccounts wxOfficialAccounts;

    @Data
    public static class FileInfo implements Serializable {

        @ApiModelProperty(value = "产品id")
        private String fileId;

        @ApiModelProperty(value = "企业账号")
        private String ea;

        /**
         * 文章使用类型com.facishare.mankeep.common.enums.FileUseTypeEnum
         */
        @ApiModelProperty(value = "文章使用类型")
        private Integer fileUseType;

        /**
         * 文章来源类型{com.facishare.mankeep.common.enums.FileBelongEnum
         */
        @ApiModelProperty(value = "文章来源类型")
        private Integer fileSourceType;

        @ApiModelProperty(value = "文件名")
        private String fileName;

        @ApiModelProperty(value = "文件拓展名")
        private String fileExtension;

        @ApiModelProperty(value = "文件大小")
        private Long fileSize;
        /*private String rootFolderId;
        private String parentFolderId;
        private String parentFolderName;
        private Integer readCount;
        private Integer creatorId;
        private String creatorName;
        private Long createTime;*/
        @ApiModelProperty(value = "是否可浏览")
        private Boolean isCanPreview;

        @ApiModelProperty(value = "图片链接")
        private String url;

        @ApiModelProperty(value = "图片缩略图链接")
        private String thumbnailUrl;
        /*private String thumbnailPath;
        private String thumbnailToken;*/
        @ApiModelProperty(value = "文件path")
        private String nPath;
    }

    @Data
    public static class ProductInfo implements Serializable {
        @ApiModelProperty(value = "产品id")
        private String id;

        @ApiModelProperty(value = "产品名")
        private String name;

        @ApiModelProperty(value = "产品头图")
        private String headPhotoUrl;
    }

    @Data
    public static class WxOfficialAccounts implements Serializable {

        @ApiModelProperty(value = "公众号appId")
        private String appId;

        @ApiModelProperty(value = "企业账号")
        private String fsEa;

        @ApiModelProperty(value = "个人企业id")
        private Integer fsUserId;

        @ApiModelProperty(value = "是否允许点击")
        private Boolean isAllowClick;
    }

}
