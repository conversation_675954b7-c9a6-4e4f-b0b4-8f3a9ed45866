package com.facishare.marketing.web.arg;

import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/5/25
 **/
@Data
@ToString(callSuper = true)
public class CreateOrUpdateCardTemplateArg extends BaseArg {

    private String id; //模板id

    private String name; //模板名称

    @Override
    public Result validateParam() {
        if (StringUtils.isBlank(this.getName())) {
            return Result.newError(SHErrorCode.PARAMS_ERROR);
        }
        return super.validateParam();
    }
}
