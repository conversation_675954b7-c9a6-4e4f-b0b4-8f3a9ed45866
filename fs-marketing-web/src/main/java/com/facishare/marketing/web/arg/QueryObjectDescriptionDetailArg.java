package com.facishare.marketing.web.arg;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import lombok.ToString;

/**
 * @Auther: dzb
 * @Date: 2018/9/20
 * @Description:
 */
@Data
@ToString(callSuper = true)
public class QueryObjectDescriptionDetailArg implements Serializable {
    @ApiModelProperty(value = "产品名称标示(try_out_product_xx)", required = true)
    private String apiName;
}
