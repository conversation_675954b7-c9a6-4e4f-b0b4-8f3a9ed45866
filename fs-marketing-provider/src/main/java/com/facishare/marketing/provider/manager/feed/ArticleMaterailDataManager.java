package com.facishare.marketing.provider.manager.feed;

import com.facishare.marketing.api.arg.marketingactivity.MarketingActivityPersonalNoticeSendArg;
import com.facishare.marketing.api.data.material.ActivityBriefData;
import com.facishare.marketing.api.data.material.ArticleBriefData;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.PhotoTargetTypeEnum;
import com.facishare.marketing.provider.dao.ArticleDAO;
import com.facishare.marketing.provider.entity.ArticleEntity;
import com.facishare.marketing.provider.entity.PhotoEntity;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.PhotoManager;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019/7/15.
 */
@Component
public class ArticleMaterailDataManager extends MaterailDataManager<ArticleBriefData> {
    @Autowired
    private ArticleDAO articleDAO;
    @Autowired
    private PhotoManager photoManager;
    @Autowired
    private FileV2Manager fileV2Manager;

    @Override
    public List<ArticleBriefData> get(String ea,String... objectIds) {
        List<ArticleBriefData> datas = Lists.newArrayList();
        List<ArticleEntity> entities = articleDAO.getByIds(Lists.newArrayList(objectIds));
        if (CollectionUtils.isNotEmpty(entities)) {
            List<String> imageUrls = Lists.newArrayList();
            for (ArticleEntity entity : entities) {
                ArticleBriefData data = new ArticleBriefData();
                data.setId(entity.getId());
                data.setSummary(entity.getSummary());
                data.setTitle(entity.getTitle());
                data.setObjectType(ObjectTypeEnum.ARTICLE.getType());
                queryPhotoThumbnailUrl(data);
                if (StringUtils.isNotEmpty(data.getPhotoThumbnailApath())) {
                    imageUrls.add(data.getPhotoThumbnailApath());
                }
                data.setCreateTime(entity.getCreateTime().getTime());
                data.setSourceType(entity.getSourceType());
                data.setUrl(entity.getUrl());
                data.setUpdateTime(entity.getLastModifyTime().getTime());
                data.setCreator(entity.getFsUserId());
                datas.add(data);
            }
            Map<String, Map<Integer, PhotoEntity>> map = photoManager.batchQueryPhotoByTypesAndIds(Lists.newArrayList(49, 50, 51), Lists.newArrayList(objectIds));
            for (ArticleBriefData data : datas) {
                Map<Integer, PhotoEntity> photoEntityMap = map.get(data.getId());
                if(photoEntityMap !=null){
                    if (photoEntityMap.get(49) != null) {
                        data.setSharePicMiniAppCutUrl(photoEntityMap.get(49).getThumbnailUrl());
                    }
                    if (photoEntityMap.get(50)  != null) {
                        data.setSharePicH5CutUrl(photoEntityMap.get(50).getThumbnailUrl());
                    }
                    if (photoEntityMap.get(51)  != null) {
                        data.setSharePicOrdinaryCutUrl(photoEntityMap.get(51).getThumbnailUrl());
                        //返回原图
                        data.setSharePicOrdinaryUrl(photoEntityMap.get(51).getUrl());
                    }
                }
            }
            //多线程处理图片大小
            Map<String, Long> coverMap = fileV2Manager.processImageSizes(ea, imageUrls);
            datas.forEach(data -> {
                if (coverMap.containsKey(data.getPhotoThumbnailApath())) {
                    data.setCoverSize(coverMap.get(data.getPhotoThumbnailApath()));
                }
            });
        }
        return datas;
    }
    private void queryPhotoThumbnailUrl(ArticleBriefData data){
        String articleId = data.getId();
        if (StringUtils.isNotEmpty(articleId)){
            List<PhotoEntity> photoEntityList = photoManager.queryPhoto(PhotoTargetTypeEnum.ARTICLE_COVER.getType(), articleId);
            if (CollectionUtils.isNotEmpty(photoEntityList)) {
                data.setPhotoThumbnailApath(photoEntityList.get(0).getPath());
                if(photoEntityList.get(0).getPath().startsWith("C_")){
                    data.setPhotoThumbnailUrl(photoEntityList.get(0).getUrl());
                }else {
                    data.setPhotoThumbnailUrl(fileV2Manager.getUrlByPath(photoEntityList.get(0).getPath(), null, false));
                }
            }
        }
    }
    @Override
    public Integer getType() {
        return ObjectTypeEnum.ARTICLE.getType();
    }
}
