package com.facishare.marketing.provider.remote.whatsapp.result;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SendMessageResult implements Serializable {

    // 通信通道类型
    @SerializedName("messaging_product")
    private String messagingProduct;

    // 消息序列
    private List<Message> messages;

    @Data
    public static class Message implements Serializable {
        // 系统生成的唯一的ID
        private String id;
    }

}
