package com.facishare.marketing.provider.remote.paas.crm;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.Data;

import java.lang.reflect.Type;

/**
 * @Auther: dzb
 * @Date: 2018/12/19
 * @Description:
 */
@Data
public class PaasCrmObjectResult<T> {

    private int code ;
    private  String message;
    private Object data;

    public static <T> PaasCrmObjectResult<T> newPaasCrmObjectResult(String responseBody,Class c) {
        PaasCrmObjectResult<T> result = new PaasCrmObjectResult<>();
        JSONObject jsonObject = JSONObject.parseObject(responseBody);
        result.message = jsonObject.getString("message");
        result.code = jsonObject.getInteger("code");
        JSONObject obj = jsonObject.getJSONObject("data");
        if(obj==null){
              return result;
        }
        Object entity =  obj.getObject("queryResult",c);
        result.data=entity;
        return result;
    }


    public static <T> PaasCrmObjectResult<T> newPaasCrmMemberResult(String responseBody,Class c) {
        PaasCrmObjectResult<T> result = new PaasCrmObjectResult<>();
        JSONObject jsonObject = JSONObject.parseObject(responseBody);
        result.message = jsonObject.getString("errMessage");
        result.code = jsonObject.getInteger("errCode");
        Object entity =  jsonObject.getObject("result",c);
        result.data=entity;
        return result;
    }

    public static <T> PaasCrmObjectResult<T> newCrmObjectForSdrResult(String responseBody, Type type) {
        PaasCrmObjectResult<T> result = new PaasCrmObjectResult<>();
        JsonObject jsonObject = CrmJsonUtil.parserToJsonObject(responseBody);
        result.message = jsonObject.get("errMessage").getAsString();
        result.code = jsonObject.get("errCode").getAsInt();
        JsonObject data = jsonObject.get("result").getAsJsonObject();
        if (data.isJsonNull()) {
            return result;
        }
        JsonElement jsonElement = data.get("dataList");
        if (jsonElement.isJsonNull()){
            return result;
        }
        if (type != null) {
            result.data = CrmJsonUtil.fromJson(jsonElement, type);
        }
        return result;
    }


}
