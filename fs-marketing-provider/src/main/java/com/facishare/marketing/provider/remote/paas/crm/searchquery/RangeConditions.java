package com.facishare.marketing.provider.remote.paas.crm.searchquery;

import java.io.Serializable;

/**
 * 对数值或日期类型的字段根据范围进行查找 Created by zhangk on 2016/7/1.
 */
@SuppressWarnings("unchecked")
public class RangeConditions implements Serializable {
    private String fieldName;
    private String timeZone;
    private String format;
    private Object from = 0;
    private Object to = 10;
    private boolean includeLower = true;
    private boolean includeUpper = true;

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String name) {
        this.fieldName = name;
    }

    public Object getFrom() {
        return from;
    }

    public void setFrom(Object from) {
        this.from = from;
    }

    public Object getTo() {
        return to;
    }

    public void setTo(Object to) {
        this.to = to;
    }

    public boolean isIncludeLower() {
        return includeLower;
    }

    public void setIncludeLower(boolean includeLower) {
        this.includeLower = includeLower;
    }

    public boolean isIncludeUpper() {
        return includeUpper;
    }

    public void setIncludeUpper(boolean includeUpper) {
        this.includeUpper = includeUpper;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public String getFormat() {
        return format;
    }

    public void setFormat(String format) {
        this.format = format;
    }

    @Override
    public String toString() {
        return "Range{" + "fieldName='" + fieldName + '\'' + ", from=" + from + ", to=" + to + '}';
    }
}