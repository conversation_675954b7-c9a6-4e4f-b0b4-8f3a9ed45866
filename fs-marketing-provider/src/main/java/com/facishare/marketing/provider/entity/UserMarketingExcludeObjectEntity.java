package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import lombok.Data;
import lombok.ToString;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

@Data
@Entity
@ToString
public class UserMarketingExcludeObjectEntity extends BaseEaEntity implements Serializable  {
    private String id;
    
    private String objectApiName;
    private String objectName;
    private Integer createBy;
    private Integer create_by;
    private Date createTime;
    private Date updateTime;
}
