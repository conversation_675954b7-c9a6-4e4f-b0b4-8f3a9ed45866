package com.facishare.marketing.provider.entity.conference;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import com.facishare.marketing.common.typehandlers.value.ButtonStyle;
import java.io.Serializable;
import java.util.Date;

import lombok.Builder;
import lombok.Data;

/**
 * Created  By zhoux 2019/07/15
 **/
@Data
@Builder
public class ConferenceEntity extends BaseEaEntity implements Serializable {

    private String id;

    

    private String marketingEventId;

    private String title;

    private Date startTime;

    private Date endTime;

    private String conferenceType;

    private String location;

    private Integer scale;

    private String conferenceDetails;

    private Date enrollEndTime;

    private Boolean enrollReview;

    private ButtonStyle enrollButton;

    /**
     * {@link com.facishare.marketing.common.enums.ActivityStatusEnum}
     */
    private Integer status;

    private Integer createBy;

    private Date createTime;

    private Integer updateBy;

    private Date updateTime;

}
