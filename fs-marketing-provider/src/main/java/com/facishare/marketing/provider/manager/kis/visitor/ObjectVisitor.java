package com.facishare.marketing.provider.manager.kis.visitor;

import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.conference.ConferenceInvitationEntity;
import com.facishare.marketing.provider.entity.file.FileEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonPageEntity;
import com.facishare.marketing.provider.entity.hexagon.HexagonSiteEntity;
import com.facishare.marketing.provider.entity.marketingplugin.WechatCouponEntity;
import com.facishare.marketing.provider.entity.officialWebsite.OfficialWebsiteEntity;
import com.facishare.marketing.provider.entity.officialWebsite.OfficialWebsiteTrackEntity;
import com.facishare.marketing.provider.entity.qr.QRPosterEntity;

/**
 * 物料访问接口
 * Created  By zhoux 2019/03/21
 **/
public interface ObjectVisitor {

    ActivityEntity visit(ActivityEntity activityEntity);

    ProductEntity visit(ProductEntity productEntity);

    ArticleEntity visit(ArticleEntity articleEntity);

    CardEntity visit(CardEntity cardEntity);

    QRPosterEntity visit(QRPosterEntity qrPosterEntity);

    ConferenceInvitationEntity visit(ConferenceInvitationEntity conferenceInvitationEntity);

    CustomizeFormDataEntity visit(CustomizeFormDataEntity customizeFormDataEntity);

    OfficialWebsiteEntity visit(OfficialWebsiteEntity officialWebsiteEntity);

    HexagonPageEntity visit(HexagonPageEntity hexagonPageEntity);

    OfficialWebsiteTrackEntity visit(OfficialWebsiteTrackEntity officialWebsiteTrackEntity);

    HexagonSiteEntity visit(HexagonSiteEntity hexagonSiteEntity);

    FileEntity visit(FileEntity fileEntity);

    WechatCouponEntity visit(WechatCouponEntity wechatCouponEntity);

    OutLinkEntity visit(OutLinkEntity entity);

    String visit(String objectId);

    VideoEntity visit(VideoEntity videoEntity);


}
