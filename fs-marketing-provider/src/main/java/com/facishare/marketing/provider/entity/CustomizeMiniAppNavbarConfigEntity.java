package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by zhengh on 2021/1/13.
 */
@Data
public class CustomizeMiniAppNavbarConfigEntity extends BaseEaEntity implements Serializable {
    private String id;
    
    private String buttonFontColor;   //tab选中的字体颜色
    private Date createTime;
    private Date updateTime;
    private Integer navbarLayout = 0; //文字图标模式  0：显示文字+图标  1：文字  3：图标
    private String fontIconColor;    //tab未选中的字体颜色
    private String backgroundColor;  //tab背景色
}
