package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import lombok.Data;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.*;

import java.util.Date;

/**
 * Created by zhengh on 2020/9/23.
 */
@Entity(value="EnterpriseSpreadRecordEntity",noClassnameStored = true)
@Indexes({
        // expireTime存放日志过期时间,创建TTL index
        @Index(fields = @Field(value = "sendTime"), options = @IndexOptions(expireAfterSeconds = 604800)),
        // 创建复合索引
        @Index(fields = {@Field("ea"), @Field(value = "type"), @Field("address"), @Field("ea")}),
})

@Data
public class EnterpriseSpreadRecordEntity extends BaseEaEntity {
    @Id
    private ObjectId _id;
    
    private Integer type;
    private String address;
    private Date sendTime;
}
