package com.facishare.marketing.provider.entity;

import com.google.common.base.Strings;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.persistence.Entity;
import lombok.Data;

@Data
@Entity
public class FormDataEntity extends CrmLeadSaveEntity implements Serializable {
    private String id;
    private String objectApiName;
    
    private Integer spreadUserId;
    private Integer objectType;
    private String objectId;
    private Integer sourceChannel;
    private String uid;
    private Integer synStatus;
    private String crmLeadId;
    private String name;
    private String phone;
    private String email;
    private String companyName;
    private String position;
    private String text1;
    private String text2;
    private String text3;
    private String text4;
    private String text5;
    private String text6;
    private String text7;
    private BigDecimal num1;
    private BigDecimal num2;
    private List<String> texts1;
    private Date createTime;
    private String leadMessage;

    @Override
    public Object getFieldValueByName(String fieldName) {
        if (Strings.isNullOrEmpty(fieldName)) {
            return null;
        }
        switch (fieldName) {
            case "name":
                return this.getName();
            case "phone":
                return this.getPhone();
            case "email":
                return this.getEmail();
            case "companyName":
                return this.getCompanyName();
            case "position":
                return this.getPosition();
            case "text1":
                return this.getText1();
            case "text2":
                return this.getText2();
            case "text3":
                return this.getText3();
            case "text4":
                return this.getText4();
            case "text5":
                return this.getText5();
            case "text6":
                return this.getText6();
            case "num1":
                return this.getNum1();
            case "num2":
                return this.getNum2();
            default:
                return null;
        }
    }
}