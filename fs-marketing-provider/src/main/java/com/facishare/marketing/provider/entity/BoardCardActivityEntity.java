package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import com.facishare.marketing.common.typehandlers.value.BoardCardActivityContentData;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2020/8/5 11:47
 * @Version 1.0
 */
@Data
public class BoardCardActivityEntity extends BaseEaEntity implements Serializable  {
    private String id;
    
    private String boardId;
    private String boardCardId;
    /**@see com.facishare.marketing.common.enums.BoardCardActivityActionTypeEnum*/
    private String actionType;
    /**操作内容*/
    private BoardCardActivityContentData content;
    /**操作者*/
    private String operator;
    private String operatorType;
    //0表示新建状态（数据库默认状态）、1表示删除状态
    private int lifeStatus;
    private Date createTime;
    private Date updateTime;
    private Boolean createBySystem;
}
