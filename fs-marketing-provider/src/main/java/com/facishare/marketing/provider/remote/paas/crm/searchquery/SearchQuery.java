package com.facishare.marketing.provider.remote.paas.crm.searchquery;

import com.google.common.collect.Lists;
import java.util.List;

/**
 * 搜索条件 Created by zhangk on 2016/7/1.
 */
public class SearchQuery {
    private Integer limit = 10;
    private Integer offset = 0;
    private List<Order> orders;
    private List<IConditions> conditions = Lists.newArrayList();
    private List<RangeConditions> rangeConditions = Lists.newArrayList();

    public SearchQuery() {

    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public List<Order> getOrders() {
        return orders;
    }

    public void setOrders(List<Order> orders) {
        this.orders = orders;
    }

    public void addCondition(IConditions condition) {
        this.conditions.add(condition);
    }

    public void addConditions(List<IConditions> conditions) {
        this.conditions.addAll(conditions);
    }

    public List<IConditions> getConditions() {
        return conditions;
    }

    public void setConditions(List<IConditions> conditions) {
        this.conditions = conditions;
    }

    public void addRangeConditions(String fieldName, Object from, Object to) {
        RangeConditions rangeCondition = new RangeConditions();
        rangeCondition.setFieldName(fieldName);
        rangeCondition.setFrom(from);
        rangeCondition.setTo(to);
        this.rangeConditions.add(rangeCondition);
    }

    public void addRangeConditions(List<RangeConditions> rangeConditionsList) {
        this.rangeConditions.addAll(rangeConditionsList);
    }

    public List<RangeConditions> getRangeConditions() {
        return rangeConditions;
    }

    public void setRangeConditions(List<RangeConditions> rangeConditions) {
        this.rangeConditions = rangeConditions;
    }
}