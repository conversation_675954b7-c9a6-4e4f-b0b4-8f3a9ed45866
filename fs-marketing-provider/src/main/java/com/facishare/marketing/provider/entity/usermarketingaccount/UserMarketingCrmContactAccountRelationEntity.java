package com.facishare.marketing.provider.entity.usermarketingaccount;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import com.facishare.marketing.common.enums.ChannelEnum;
import com.google.common.base.Joiner;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 11/12/2018
 */
@Data
public class UserMarketingCrmContactAccountRelationEntity extends BaseEaEntity implements Serializable  {
    private static final long serialVersionUID = 1L;
    private String id;
    /**
     * CRM联系人id
     */
    private String crmContactId;
    /**
     * 企业ea
     */
    
    /**
     * 用户营销账号id
     */
    private String userMarketingId;
    private Date createTime;
    private Date updateTime;
    private String userName;
    private String email;

    public String generateUniqueKey() {
       return Joiner.on(".").join(ChannelEnum.MINIAPP.name(), this.getEa(), this.getCrmContactId());
    }

}
