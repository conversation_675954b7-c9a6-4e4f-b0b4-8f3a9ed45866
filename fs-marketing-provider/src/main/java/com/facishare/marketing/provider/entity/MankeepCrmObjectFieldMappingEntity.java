package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import com.facishare.marketing.api.result.FieldMappingConfigResult;
import com.facishare.marketing.api.result.FieldMappingResult;
import com.facishare.marketing.common.enums.MankeepObjectApiNameEnum;
import com.facishare.marketing.common.typehandlers.value.FieldMappings;
import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;
import javax.persistence.Entity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Entity
public class MankeepCrmObjectFieldMappingEntity extends BaseEaEntity implements Serializable  {
    private static final long serialVersionUID = 1L;
    private String id;
    
    /** 保存到CRM的客脉对象 {@link MankeepObjectApiNameEnum} */
    private String mankeepObjectApiName;
    /** 保存到CRM的CRM对象 {@link com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum} */
    private String crmObjectApiName;
    /** CRM对象的业务类型 */
    private String crmObjectRecordType;
    /** 字段映射关系 */
    private FieldMappings fieldMappings;
    /** 字段映射关系 */
    private FieldMappings fieldMappingsV2;

    public FieldMappingConfigResult toFieldMappingConfigVO() {
        FieldMappingConfigResult fieldMappingConfigResult = new FieldMappingConfigResult();
        fieldMappingConfigResult.setMankeepObjectApiName(this.mankeepObjectApiName);
        fieldMappingConfigResult.setCrmObjectApiName(this.crmObjectApiName);
        fieldMappingConfigResult.setRecordType(this.crmObjectRecordType);
        if (fieldMappingsV2 != null) {
            List<FieldMappingResult> fieldMappingResultList = fieldMappingsV2.stream().map(fieldMapping -> {
                FieldMappingResult fieldMappingResult = new FieldMappingResult();
                fieldMappingResult.setMankeepFieldName(fieldMapping.getMankeepFieldName());
                fieldMappingResult.setDefaultValue(fieldMapping.getDefaultValue());
                fieldMappingResult.setCrmFieldName(fieldMapping.getCrmFieldName());
                fieldMappingResult.setModifiable(fieldMapping.isModifiable());
                return fieldMappingResult;
            }).collect(Collectors.toList());
            fieldMappingConfigResult.setFieldMappings(fieldMappingResultList);
        }
        return fieldMappingConfigResult;
    }
}
