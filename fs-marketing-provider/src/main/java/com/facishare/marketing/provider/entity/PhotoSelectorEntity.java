package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class PhotoSelectorEntity extends BaseEaEntity implements Serializable {
    private String id;
    
    private Integer createBy;
    private String photoName;
    private Long photoSize;
    private String photoPath;
    private String thumbailPath;
    private String ext;
    private String cdnUrl;
    private Date createTime;
    private Date updateTime;
}
