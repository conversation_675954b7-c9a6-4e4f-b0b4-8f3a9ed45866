package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * Created  By zhoux 2020/05/22
 **/
@Data
public class ResetDataStatusEntity extends BaseEaEntity implements Serializable  {

    private String id;

    

    private Integer fsUserId;

    private Integer type;

    private Integer status;

    private Date createTime;

    private Date updateTime;

}
