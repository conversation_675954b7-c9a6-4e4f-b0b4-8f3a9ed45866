package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Created on 2021-02-24.
 */
@Data
public class TriggerSopTaskEntity extends BaseEaEntity implements Serializable  {
	
	private String triggerId;
	private String triggerSnapshotId;
	private String triggerTaskSnapshotId;
	private String triggerTaskInstanceId;
	private Long executeTime;
	private Integer type;
	private String groupSender;
	private Integer owner;
	private String ownerUserId;
	private String toUser;
	private Integer toSize;
	private Long taskEnd;
}
