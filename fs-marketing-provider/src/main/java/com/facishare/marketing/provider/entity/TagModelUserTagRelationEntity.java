package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TagModelUserTagRelationEntity extends BaseEaEntity implements Serializable  {
    
    private String tagModelId;
    private String parentTagId;
    private String userTagId;
    private Integer operatorId;
    private Date createTime;
    private Date updateTime;
}
