package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import com.facishare.marketing.common.typehandlers.value.TagNameList;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by ranluch on 2019/10/31.
 */
@Data
public class ObjectTagEntity extends BaseEaEntity implements Serializable  {
    private String id;
    
    private String objectId;
    private Integer objectType;
    /** 标签Name列表 */
    private TagNameList tagNameList;

    private Date createTime;
    private Date updateTime;
}
