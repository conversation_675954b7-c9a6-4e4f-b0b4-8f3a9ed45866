package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import java.io.Serializable;
import lombok.Data;

@Data
public class TriggerTaskInstanceAndSnapshotEntity extends BaseEaEntity implements Serializable  {
    private String triggerInstanceId;
    private String triggerSnapshotId;
    private String triggerTaskInstanceId;
    private String triggerTaskSnapshotId;
    private String executeStatus;
    private String executeResult;
    private String taskType;
    private String marketingUserId;
}
