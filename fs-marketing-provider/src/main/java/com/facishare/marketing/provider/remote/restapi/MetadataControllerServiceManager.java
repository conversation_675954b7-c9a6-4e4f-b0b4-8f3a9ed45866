package com.facishare.marketing.provider.remote.restapi;

import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.api.arg.ListLeadInfosArg;
import com.facishare.marketing.api.arg.PageArg;
import com.facishare.marketing.api.result.OfficialWebsiteLeadsDataResult;
import com.facishare.marketing.api.result.clueManagement.GetCustomerServiceClueMappingResult;
import com.facishare.marketing.api.service.ClueManagementService;
import com.facishare.marketing.common.annoation.FilterLog;
import com.facishare.marketing.common.enums.officialWebsite.OnlineServiceDeviceEnum;
import com.facishare.marketing.common.exception.MarketingWithoutExceptionInfoException;
import com.facishare.marketing.common.exception.OuterServiceRuntimeException;
import com.facishare.marketing.common.result.PageResult;
import com.facishare.marketing.common.typehandlers.value.FieldMappings;
import com.facishare.rest.core.util.JsonUtil;
import com.fxiaoke.crmrestapi.arg.ControllerDetailArg;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.contants.CrmErrorCode;
import com.fxiaoke.crmrestapi.common.contants.FilterOperatorEnum;
import com.fxiaoke.crmrestapi.common.contants.LeadsFieldContants;
import com.fxiaoke.crmrestapi.common.data.*;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.service.MetadataControllerService;
import com.google.common.collect.Lists;
import jetbrick.util.JSONUtils;
import jetbrick.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019/7/15.
 */
@Service
@Slf4j
public class MetadataControllerServiceManager {
    @Autowired
    private MetadataControllerService metadataControllerService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private ClueManagementService clueManagementService;

    public Page<ObjectData> list(HeaderObj headerObj, String apiName, ControllerListArg arg) {
        Result<Page<ObjectData>> result = this.listResults(headerObj, apiName, arg);
        arg.setIncludeInvalidData(false);
        arg.setIncludeButtonInfo(false);
        arg.setIncludeLayout(false);
        arg.setIncludeDescribe(false);
        if (!result.isSuccess()) {
            throw new OuterServiceRuntimeException(result.getCode(), result.getMessage());
        }
        return result.getData();
    }

    @FilterLog
    public Result<Page<ObjectData>> listResults(HeaderObj headerObj, String apiName, ControllerListArg arg) {
        Result<Page<ObjectData>> result = metadataControllerService.list(headerObj, apiName, arg);
        arg.setIncludeInvalidData(false);
        arg.setIncludeButtonInfo(false);
        arg.setIncludeLayout(false);
        arg.setIncludeDescribe(false);

        return result;
    }

    public Integer getTotal(HeaderObj headerObj, String apiName, ControllerListArg arg) {
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setLimit(1);
        searchQuery.setOffset(0);
        ControllerListArg getTotalArg = JSON.parseObject(JSON.toJSONString(arg), ControllerListArg.class);
        getTotalArg.setObjectDescribeApiName(apiName);
        getTotalArg.setFieldProjection(Lists.newArrayList("_id"));
        Result<Page<ObjectData>> result = metadataControllerService.list(headerObj, apiName, getTotalArg);
        if (!result.isSuccess()) {
            throw new OuterServiceRuntimeException(result.getCode(), result.getMessage());
        }
        log.info("ei:{} apiName:{} getTotal:{}", headerObj.get("X-fs-ei"), apiName, result);
        return result.getData().getTotal();
    }

    public Integer getTotalOnlyId(HeaderObj headerObj, String apiName, ControllerListArg arg) {
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setLimit(1);
        searchQuery.setOffset(0);
        // 只查询_id字段即可
        arg.setFieldProjection(Lists.newArrayList("_id"));
        Result<Page<ObjectData>> result = metadataControllerService.list(headerObj, apiName, arg);
        if (!result.isSuccess()) {
            throw new OuterServiceRuntimeException(result.getCode(), result.getMessage());
        }
        return result.getData().getTotal();
    }

    public ObjectData detail(HeaderObj headerObj, String apiName, ControllerDetailArg arg) {
        arg.setIsSimpleData(true);
        arg.setIncludeLayout(false);
        arg.setIncludeDescribe(false);
        Result<ControllerGetDescribeResult> result = metadataControllerService.detail(headerObj, apiName, arg);

        if (!result.isSuccess()) {
            throw new OuterServiceRuntimeException(result.getCode(), result.getMessage());
        }
        return result.getData().getData();
    }

    @FilterLog
    public ObjectData detailForMq(HeaderObj headerObj, String apiName, ControllerDetailArg arg) {
        arg.setIsSimpleData(true);
        arg.setIncludeLayout(false);
        arg.setIncludeDescribe(false);
        Result<ControllerGetDescribeResult> result = metadataControllerService.detail(headerObj, apiName, arg);

        if (!result.isSuccess()) {
            throw new OuterServiceRuntimeException(result.getCode(), result.getMessage());
        }
        return result.getData().getData();
    }

    public Result<ControllerGetDescribeResult> detailSimpleResult(HeaderObj headerObj, String apiName, ControllerDetailArg arg) {
        arg.setIsSimpleData(true);
        arg.setIncludeDescribe(false);
        arg.setIncludeLayout(false);
        Result<ControllerGetDescribeResult> result = metadataControllerService.detail(headerObj, apiName, arg);
        return result;
    }


    public Optional<List<ObjectData>> pageQueryCrmMarketingKeyword(int ei, Integer fsUserId, String apiName, Integer offset, Integer endOffset, Integer perSize){
        ControllerListArg params = new ControllerListArg();
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setLimit(endOffset - offset <= perSize ? endOffset - offset : perSize);
        searchQuery.setOffset(offset);
        params.setSearchQuery(searchQuery);
        Result<Page<ObjectData>> result = listResults(new HeaderObj(ei, fsUserId), apiName, params);
        if (!result.isSuccess() && result.getCode() == CrmErrorCode.NO_PERMISSION) {
            throw new MarketingWithoutExceptionInfoException(result.getCode(), result.getMessage());
        } else if (!result.isSuccess() && result.getCode() != CrmErrorCode.NO_PERMISSION) {
            throw new OuterServiceRuntimeException(result.getCode(), result.getMessage());
        }
        Page<ObjectData> dataPage = result.getData();
        if (dataPage == null || CollectionUtils.isEmpty(dataPage.getDataList())){
            return Optional.empty();
        }
        return Optional.of(dataPage.getDataList());
    }


    /**
     * 分页查询线索
     *
     * @param ei                    enterpriseId
     * @param fsUserId              员工纷享id
     * @param arg                   线索查询条件
     * @param pageArg               分页参数
     * @return                      线索的分页列表
     */
    public PageResult<OfficialWebsiteLeadsDataResult> listCrmLeads(Integer ei, Integer fsUserId, ListLeadInfosArg arg, PageArg pageArg) {
        List<OfficialWebsiteLeadsDataResult> officialWebsiteLeadsDataResults = Lists.newArrayList();
        com.facishare.marketing.common.result.Result<GetCustomerServiceClueMappingResult> clueMapping = clueManagementService.getCustomerServiceClueMapping(eieaConverter.enterpriseIdToAccount(ei));
        //如果没有53快服的配置,则全都是在线客服
        if (!clueMapping.isSuccess() || clueMapping.getData() == null || clueMapping.getData().getCrmFormFieldMap() == null) {
            if( clueMapping.getData() == null || clueMapping.getData().getCrmFormFieldMap() == null){
                ControllerListArg params = new ControllerListArg();
                SearchQuery searchQuery = this.buildSearchQuery(arg);
                params.setSearchQuery(searchQuery);
                params.setObjectDescribeApiName(LeadsFieldContants.API_NAME);
                if(StringUtils.isNotEmpty(arg.getKeyword())){
                    params.setKeyword(arg.getKeyword());
                }
                Integer total = this.getTotal(new HeaderObj(ei, fsUserId), LeadsFieldContants.API_NAME, params);
                if(total==0){
                    return PageResult.newPageResult(pageArg.getPageNo(), pageArg.getPageSize(), 0, officialWebsiteLeadsDataResults);
                }
                searchQuery.setLimit(pageArg.getPageSize());
                searchQuery.setOffset(pageArg.getOffset());
                params.setSearchQuery(searchQuery);
                Result<Page<ObjectData>> result = this.listResults(new HeaderObj(ei, fsUserId), LeadsFieldContants.API_NAME, params);
                if(result.isSuccess() && result.getData()!=null && CollectionUtils.isNotEmpty(result.getData().getDataList())){
                    buildOfficialWebsiteChatServiceDataResult(officialWebsiteLeadsDataResults, result);
                    return PageResult.newPageResult(pageArg.getPageNo(), pageArg.getPageSize(), total, officialWebsiteLeadsDataResults);
                }
            }
            log.info("OfficialWebsiteServiceImpl.getOfficialWebsiteLeads getCustomerServiceClueMapping fail, ea:{}", ei);
            return PageResult.newPageResult(pageArg.getPageNo(), pageArg.getPageSize(), 0, officialWebsiteLeadsDataResults);
        }
        FieldMappings crmFormFieldMap = clueMapping.getData().getCrmFormFieldMap();
        Integer perSize = 100;
        Integer offset;
        Integer endOffset;
        ControllerListArg params = new ControllerListArg();
        SearchQuery searchQuery = this.buildSearchQuery(arg);
        params.setSearchQuery(searchQuery);
        params.setObjectDescribeApiName(LeadsFieldContants.API_NAME);
        if(StringUtils.isNotEmpty(arg.getKeyword())){
            params.setKeyword(arg.getKeyword());
        }
        Integer total = this.getTotal(new HeaderObj(ei, fsUserId), LeadsFieldContants.API_NAME, params);
        if (pageArg == null) {
            endOffset = total;
            offset = 0;
        } else {
            endOffset = pageArg.getOffset() + pageArg.getPageSize();
            offset = pageArg.getOffset();
        }
        for (; offset < endOffset; offset += perSize) {
            searchQuery.setLimit(endOffset - offset <= perSize ? endOffset - offset : perSize);
            searchQuery.setOffset(offset);
            params.setSearchQuery(searchQuery);
            Result<Page<ObjectData>> result = this.listResults(new HeaderObj(ei, fsUserId), LeadsFieldContants.API_NAME, params);
            if (!result.isSuccess() && result.getCode() == CrmErrorCode.NO_PERMISSION) {
                throw new MarketingWithoutExceptionInfoException(result.getCode(), result.getMessage());
            } else if (!result.isSuccess() && result.getCode() != CrmErrorCode.NO_PERMISSION) {
                throw new OuterServiceRuntimeException(result.getCode(), result.getMessage());
            }
            Page<ObjectData> dataPage = result.getData();
            List<LeadsData> leadsDataList = convert2LeadsDataList(dataPage.getDataList());
            officialWebsiteLeadsDataResults.addAll(convert2OfficialWebsiteLeadsDataResults(eieaConverter.enterpriseIdToAccount(ei), leadsDataList));
        }
        PageResult<OfficialWebsiteLeadsDataResult> pageResult = PageResult.newPageResult(pageArg.getPageNo(), pageArg.getPageSize(), total, officialWebsiteLeadsDataResults);
        FieldMappings.FieldMapping fieldMap = new FieldMappings.FieldMapping();
        fieldMap.setMankeepFieldName("create_time");
        fieldMap.setCrmFieldName("create_time");
        crmFormFieldMap.add(fieldMap);
        pageResult.setOtherData(crmFormFieldMap.stream().filter(fieldMapping -> StringUtils.isNotEmpty(fieldMapping.getCrmFieldName()) && !StringUtils.equals(fieldMapping.getMankeepFieldName(), "land_page") && !StringUtils.equals(fieldMapping.getMankeepFieldName(), "qq")).collect(Collectors.toList()));
        return pageResult;
    }

    private void buildOfficialWebsiteChatServiceDataResult(List<OfficialWebsiteLeadsDataResult> officialWebsiteLeadsDataResults, Result<Page<ObjectData>> result) {
        for(ObjectData data  : result.getData().getDataList()){
            OfficialWebsiteLeadsDataResult leadsDataResult = new OfficialWebsiteLeadsDataResult();
            OfficialWebsiteLeadsDataResult.PresetData presetData = new OfficialWebsiteLeadsDataResult.PresetData();
            leadsDataResult.setPresetData(presetData);
            presetData.setLeadId(data.getId());
            presetData.setName(data.getName());
            presetData.setPhone(StringUtils.isNotEmpty(data.getString("mobile")) ? data.getString("mobile") : data.getString("tel"));
            presetData.setCreate_time(data.getCreateTime());
            presetData.setEmail(data.getString("email"));
            presetData.setProvince_city(data.getCity());
            //utm参数改为必返回,不用根据配置
            if (StringUtils.isNotBlank(data.getString("utm_medium__c"))) {
                presetData.setUtmMedium(data.getString("utm_medium__c"));
            }

            if (StringUtils.isNotBlank(data.getString("utm_source__c"))) {
                presetData.setUtmSource(data.getString("utm_source__c"));
            }

            if (StringUtils.isNotBlank(data.getString("utm_campaign__c"))) {
                presetData.setUtmCampaign(data.getString("utm_campaign__c"));
            }

            if (StringUtils.isNotBlank(data.getString("utm_content__c"))) {
                presetData.setUtmContent(data.getString("utm_content__c"));
            }

            if (StringUtils.isNotBlank(data.getString("utm_term__c"))) {
                presetData.setUtmTerm(data.getString("utm_term__c"));
            }
            OfficialWebsiteLeadsDataResult.OtherCrmObjectBind otherCrmObjectBind = new OfficialWebsiteLeadsDataResult.OtherCrmObjectBind();
            leadsDataResult.setOtherCrmObjectBind(otherCrmObjectBind);
            otherCrmObjectBind.setObjectId(data.getId());
            officialWebsiteLeadsDataResults.add(leadsDataResult);
        }
    }


    /**
     * 将leadsDataList转换为List<OfficialWebsiteLeadsDataResult>
     *
     * @param ea                    企业账号
     * @param leadsDataList         crm线索数据
     * @return                      List<OfficialWebsiteLeadsDataResult>
     */
    public List<OfficialWebsiteLeadsDataResult> convert2OfficialWebsiteLeadsDataResults(String ea, List<LeadsData> leadsDataList) {
        List<OfficialWebsiteLeadsDataResult> officialWebsiteLeadsDataResultList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(leadsDataList)) {
            log.info("MetadataControllerServiceManager.convert2OfficialWebsiteLeadsDataResults leadsDataList is null, leadsDataList:{}", leadsDataList);
            return officialWebsiteLeadsDataResultList;
        }
        for (LeadsData leadsData : leadsDataList) {
            OfficialWebsiteLeadsDataResult result = new OfficialWebsiteLeadsDataResult();
            buildOfficialWebsiteLeadsDataResult(ea ,leadsData, result);
            officialWebsiteLeadsDataResultList.add(result);
        }
        return officialWebsiteLeadsDataResultList;
    }


    /**
     * 构建OfficialWebsiteLeadsDataResult
     * 支持不同的客户设置不同的映射
     *
     * @param ea                企业账号
     * @param leadsData         crm线索数据
     */
    private void buildOfficialWebsiteLeadsDataResult(String ea, LeadsData leadsData, OfficialWebsiteLeadsDataResult result) {
        com.facishare.marketing.common.result.Result<GetCustomerServiceClueMappingResult> clueMapping = clueManagementService.getCustomerServiceClueMapping(ea);
        if (!clueMapping.isSuccess() || clueMapping.getData() == null || clueMapping.getData().getCrmFormFieldMap() == null) {
            log.info("OfficialWebsiteServiceImpl.getOfficialWebsiteLeads getCustomerServiceClueMapping fail, ea:{}", ea);
            return;
        }
        FieldMappings fieldMaps = clueMapping.getData().getCrmFormFieldMap();
        OfficialWebsiteLeadsDataResult.PresetData presetData = new OfficialWebsiteLeadsDataResult.PresetData();
        Map<String, Object> customData  = new HashMap<>();
        OfficialWebsiteLeadsDataResult.OtherCrmObjectBind otherCrmObjectBind = new OfficialWebsiteLeadsDataResult.OtherCrmObjectBind();
        for (FieldMappings.FieldMapping fieldMap : fieldMaps) {
            if (StringUtils.isEmpty(fieldMap.getCrmFieldName())) {
                continue;
            }
            // 设置预设字段数据
            if (StringUtils.equals(fieldMap.getMankeepFieldName(), "name")) {
                presetData.setName(leadsData.get(fieldMap.getCrmFieldName()) !=null ? String.valueOf(leadsData.get(fieldMap.getCrmFieldName())) : null);
            }
            if (StringUtils.equals(fieldMap.getMankeepFieldName(), "phone")) {
                presetData.setPhone(leadsData.get(fieldMap.getCrmFieldName()) != null ? String.valueOf(leadsData.get(fieldMap.getCrmFieldName())) : null);
            }
            if (StringUtils.equals(fieldMap.getMankeepFieldName(), "email")) {
                presetData.setEmail(leadsData.get(fieldMap.getCrmFieldName()) != null ? String.valueOf(leadsData.get(fieldMap.getCrmFieldName())) : null);
            }
            if (StringUtils.equals(fieldMap.getMankeepFieldName(), "province_city")) {
                presetData.setProvince_city(leadsData.get(fieldMap.getCrmFieldName()) != null ? String.valueOf(leadsData.get(fieldMap.getCrmFieldName())) : null);
            }
            if (StringUtils.equals(fieldMap.getMankeepFieldName(), "se")) {
                presetData.setSe(leadsData.get(fieldMap.getCrmFieldName()) != null ? String.valueOf(leadsData.get(fieldMap.getCrmFieldName())) : null);
            }
            if (StringUtils.equals(fieldMap.getMankeepFieldName(), "kw")) {
                presetData.setKw(leadsData.get(fieldMap.getCrmFieldName()) != null ? String.valueOf(leadsData.get(fieldMap.getCrmFieldName())) : null);
            }
            if (StringUtils.equals(fieldMap.getMankeepFieldName(), "device")) {
                Integer deviceType = leadsData.get(fieldMap.getCrmFieldName()) != null ? Integer.valueOf(String.valueOf(leadsData.get(fieldMap.getCrmFieldName()))) : null;
                presetData.setDevice(OnlineServiceDeviceEnum.getDescByType(deviceType));
            }
            if (StringUtils.equals(fieldMap.getMankeepFieldName(), "talk_page")) {
                presetData.setTalk_page(leadsData.get(fieldMap.getCrmFieldName()) != null ? String.valueOf(leadsData.get(fieldMap.getCrmFieldName())) : null);
            }
            if (StringUtils.equals(fieldMap.getMankeepFieldName(), "work_id")) {
                presetData.setWork_id(leadsData.get(fieldMap.getCrmFieldName()) != null ? String.valueOf(leadsData.get(fieldMap.getCrmFieldName())) : null);
            }
            //utm参数改为必返回,不用根据配置
            if (StringUtils.isNotBlank(leadsData.getString("utm_medium__c"))) {
                presetData.setUtmMedium(leadsData.getString("utm_medium__c"));
            }

            if (StringUtils.isNotBlank(leadsData.getString("utm_source__c"))) {
                presetData.setUtmSource(leadsData.getString("utm_source__c"));
            }

            if (StringUtils.isNotBlank(leadsData.getString("utm_campaign__c"))) {
                presetData.setUtmCampaign(leadsData.getString("utm_campaign__c"));
            }

            if (StringUtils.isNotBlank(leadsData.getString("utm_content__c"))) {
                presetData.setUtmContent(leadsData.getString("utm_content__c"));
            }

            if (StringUtils.isNotBlank(leadsData.getString("utm_term__c"))) {
                presetData.setUtmTerm(leadsData.getString("utm_term__c"));
            }
            // 客户自定义数据 mankeepFieldName是空的 CrmFieldName不为空
            if (StringUtils.isNotEmpty(fieldMap.getCrmFieldName()) && StringUtils.isEmpty(fieldMap.getMankeepFieldName())) {
                customData.put(fieldMap.getCrmFieldName(), leadsData.get(fieldMap.getCrmFieldName()));
            }
        }
        presetData.setCreate_time(leadsData.getCreateTime());
        presetData.setLeadId(leadsData.getId());
        otherCrmObjectBind.setObjectId(leadsData.getId());
        result.setPresetData(presetData);
        result.setCustomData(customData);
        result.setOtherCrmObjectBind(otherCrmObjectBind);
    }


    /**
     * 将crm对象list转换为LeadsDatas
     *
     * @param objectDataList        crm对象list
     * @return                      List<LeadsData>
     */
    public List<LeadsData> convert2LeadsDataList(List<ObjectData> objectDataList) {
        List<LeadsData> leadsDataList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(objectDataList)) {
            log.info("MetadataControllerServiceManager.convert2LeadsDataList objectDataList is null, objectDataList:{}", objectDataList);
            return leadsDataList;
        }
        Iterator var2 = objectDataList.iterator();

        while(var2.hasNext()) {
            ObjectData objectData = (ObjectData)var2.next();
            LeadsData data = LeadsData.wrap(objectData);
            leadsDataList.add(data);
        }
        return leadsDataList;
    }


    /**
     * 构建SearchQuery查询参数
     *
     * @param arg               线索查询条件
     * @return                  SearchQuery
     */
    private SearchQuery buildSearchQuery(ListLeadInfosArg arg) {
        SearchQuery searchQuery = new SearchQuery();
        searchQuery.addFilter("promotion_channel", Lists.newArrayList(arg.getChannelValue()), FilterOperatorEnum.EQ);
        return searchQuery;
    }
}
