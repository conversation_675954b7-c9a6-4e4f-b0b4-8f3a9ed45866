package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import com.facishare.marketing.common.typehandlers.value.CustomTagSetting;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class MarketingEvenCustomTagSettingEntity extends BaseEaEntity implements Serializable  {

    private String id;
    
    private CustomTagSetting customTagSetting;
    private Date createTime;
    private Date updateTime;

}
