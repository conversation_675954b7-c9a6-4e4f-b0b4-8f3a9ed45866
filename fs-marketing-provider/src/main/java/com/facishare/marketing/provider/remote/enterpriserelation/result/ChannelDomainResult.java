package com.facishare.marketing.provider.remote.enterpriserelation.result;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/11/25
 * @Desc
 **/
@Data
public class ChannelDomainResult implements Serializable {
    /**
     * 域名ID
     */
    private String id;
    /**
     * 上游企业ID
     */
    private String upstreamEi;
    /**
     * 域名
     */
    private String domainName;
    /**
     * 域名全称
     */
    private String domainFullName;
    /**
     * 域名别名
     */
    private String cname;
    /**
     * 绑定的自定义登录页面
     */
    private String loginTemplateId;
    /**
     * 审核状态:0-待审核，1-审核通过，2-审核不通过
     */
    private Integer auditState;
    /**
     * 审核结果
     */
    private String auditResult;
    /**
     * 审核时间
     */
    private String auditTime;
    /**
     * 申请人
     */
    private String proposer;
    /**
     * 域名类型：0-专属域名；1-自定义域名
     */
    private Integer type;
}
