package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Entity;
import lombok.Data;
import lombok.ToString;

/**
 * @author: dongzhb
 * @date: 2019/3/4
 * @Description:
 */
@Data
@Entity
@ToString
public class EnterpriseMetaConfigLogEntity extends BaseEaEntity implements Serializable  {
    private static final long serialVersionUID = -341653263306372664L;
    
    /**状态 0 未开始 1 成功 2 失败*/
    private Integer status;
    /**创建时间*/
    private Date createTime;
    /**状态码*/
    private Integer code;
    /**返回信息*/
    private String message;
}
