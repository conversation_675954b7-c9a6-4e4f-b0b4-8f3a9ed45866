package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

@Data
@Entity
public class BrowserUserEntity extends BaseEaEntity implements Serializable  {
    private String id;
    private String userAgent;
    private Date createTime;
    private Date updateTime;
}
