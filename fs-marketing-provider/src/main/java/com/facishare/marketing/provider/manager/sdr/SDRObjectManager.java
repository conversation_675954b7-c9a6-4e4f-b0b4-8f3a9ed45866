package com.facishare.marketing.provider.manager.sdr;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.common.enums.ChannelEnum;
import com.facishare.marketing.common.enums.MarketingPluginTypeEnum;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.provider.innerArg.AssociationArg;
import com.facishare.marketing.provider.innerResult.AssociationResult;
import com.facishare.marketing.provider.manager.MarketingPluginConfigManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountAssociationManager;
import com.facishare.marketing.provider.manager.usermarketingaccount.UserMarketingAccountRelationManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.FieldDescribeService;
import com.facishare.marketing.provider.remote.arg.AssignRecordArg;
import com.facishare.marketing.provider.remote.arg.FindRoleAndRecordTypeArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryArg;
import com.facishare.marketing.provider.remote.paas.crm.arg.PaasQueryFilterArg;
import com.facishare.marketing.provider.remote.result.FindRoleAndRecordTypeResult;
import com.facishare.marketing.provider.remote.result.SfaIntegralResult;
import com.fxiaoke.crmrestapi.arg.AddDescribeCustomFieldArg;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.InnerPage;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.ObjectDescribe;
import com.fxiaoke.crmrestapi.result.ControllerGetDescribeResult;
import com.fxiaoke.crmrestapi.service.ObjectDescribeCrmService;
import com.fxiaoke.crmrestapi.service.ObjectDescribeService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component("SDRObjectManager")
public class SDRObjectManager {

    @Autowired
    private FieldDescribeService fieldDescribeService;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private CrmV2Manager crmV2Manager;

    @Autowired
    private ObjectDescribeService objectDescribeService;

    @Autowired
    private ObjectDescribeCrmService objectDescribeCrmService;

    @Autowired
    private MarketingPluginConfigManager marketingPluginConfigManager;

    @Autowired
    private UserMarketingAccountRelationManager userMarketingAccountRelationManager;

    @Autowired
    private UserMarketingAccountAssociationManager userMarketingAccountAssociationManager;

    public void initSDRConfig(String ea) {
        CrmObjectApiNameEnum.getAllSdrApiNames().forEach(e -> assignRecord(ea, e.getName()));
        initSDRScoringModelObj(ea);
        initSDRScoringDimensionObj(ea);
        addMarketingVisitorFieldToWebImVisitorObj(ea);
    }

    public void addMarketingVisitorFieldToWebImVisitorObj(String ea) {
        try {
            HeaderObj systemHeader = new HeaderObj(eieaConverter.enterpriseAccountToId(ea), SuperUserConstants.USER_ID);
            Integer ei = eieaConverter.enterpriseAccountToId(ea);
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> objectDescribeResult = objectDescribeService.getDescribe(new HeaderObj(ei, SuperUserConstants.USER_ID), CrmObjectApiNameEnum.WEB_IM_VISITOR_OBJ.getName());
            if (objectDescribeResult == null || !objectDescribeResult.isSuccess()) {
                log.warn("WebImVisitorObj desc is not exist ea: {}", ea);
                return;
            }

            ObjectDescribe objectDescribe = objectDescribeResult.getData().getDescribe();
            if (objectDescribe.getFields().containsKey("marketing_visitor_id")) {
                log.info("WebImVisitorObj already exist marketing_visitor_id field， ea: {}", ea);
                return;
            }
            AddDescribeCustomFieldArg arg = new AddDescribeCustomFieldArg();
            arg.setDescribeAPIName(CrmObjectApiNameEnum.WEB_IM_VISITOR_OBJ.getName());
            arg.setFieldDescribe("{\"describe_api_name\":\"WebImVisitorObj\",\"default_is_expression\":false,\"auto_adapt_places\":false,\"description\":\"营销通访客id\",\"is_unique\":false,\"type\":\"text\",\"default_to_zero\":false,\"is_required\":false,\"define_type\":\"package\",\"is_single\":false,\"label_r\":\"营销通访客id\",\"is_index\":true,\"is_active\":true,\"is_encrypted\":false,\"default_value\":\"\",\"label\":\"营销通访客id\",\"api_name\":\"marketing_visitor_id\",\"is_index_field\":true,\"config\":{},\"help_text\":\"\",\"status\":\"released\",\"is_extend\":false}");
            com.fxiaoke.crmrestapi.common.result.Result<ControllerGetDescribeResult> result = objectDescribeCrmService.addDescribeCustomField(systemHeader, arg);
            log.info("addMarketingVisitorFieldToWebImVisitorObj ea:{} result: {}", ea, result);
        } catch (Exception e) {
            log.error("addYxtVisitorFieldToWebImVisitorObj error, ea: {}", ea, e);
        }

    }

    public void initSDRScoringDimensionObj(String ea) {
        List<String> modelNameList = Lists.newArrayList("BANT", "画像/意向评分");
        List<ObjectData> modelObjectDataList  = getObjectListByName(ea, modelNameList, CrmObjectApiNameEnum.SDR_SCORING_MODEL_OBJ.getName());
        Map<String, String> modelNameToIdMap = modelObjectDataList.stream().collect(Collectors.toMap(ObjectData::getName, ObjectData::getId, (v1, v2) -> v1));
        Set<String> bantDimensionNameSet = Sets.newHashSet("预算", "权限", "需求", "时间");
        List<String> dimensionNameList = Lists.newArrayList("意向度", "画像匹配度");
        dimensionNameList.addAll(bantDimensionNameSet);
        List<ObjectData> dimensionObjectDataList  = getObjectListByName(ea, dimensionNameList, CrmObjectApiNameEnum.SDR_SCORING_DIMENSION_OBJ.getName());
        Set<String> existNameSet = dimensionObjectDataList.stream().map(ObjectData::getName).collect(Collectors.toSet());

        String jsonData = "[{\"model_name\":\"\",\"name\":\"预算\",\"dimension_criteria\":\"0: 无预算信息\\n60: 预算推测明确\\n80: 预算基本明确\\n100: 预算充足\",\"dimension_status\":\"enabled\",\"dimension_weight\":10},{\"model_name\":\"\",\"name\":\"权限\",\"dimension_criteria\":\"0: 无决策权信息 \\n60:推测有决策权 \\n80:有决策权 \\n100:主要决策人\",\"dimension_status\":\"enabled\",\"dimension_weight\":10},{\"model_name\":\"\",\"name\":\"需求\",\"dimension_criteria\":\"0: 需求未识别 \\n20: 初步需求模糊 \\n40: 需求部分明确 \\n60: 需求匹配解决方案 \\n80: 需求清晰 \\n100: 完全量化\",\"dimension_status\":\"enabled\",\"dimension_weight\":60},{\"model_name\":\"\",\"name\":\"时间\",\"dimension_criteria\":\"0: 没有识别到客户推进采购 \\n40:识别到客户有推进采购动作 \\n60:有大致采购计划 \\n80:有大致采购时间 \\n100:有明确采购时间\",\"dimension_status\":\"enabled\",\"dimension_weight\":20},{\"model_name\":\"\",\"name\":\"意向度\",\"dimension_criteria\":\"0: 用户近90天访问过的内容主题中没有识别到含义相近的行业关键词 \\n20:用户近90天访问过的访问内容主题中识别到含义相近的行业关键词 \\n40:用户近90天访问过的访问内容主题中识别到含义相近的行业关键词，访问累计时长达1分钟 \\n60:用户近90天访问过的访问内容主题中识别到1个以上含义相近的行业关键词，或者识别到含义相近的行业关键词访问累计时长达3分钟 \\n80:用户近90天访问过的访问内容主题中识别到2个以上含义相近的行业关键词，或者识别到含义相近的行业关键词访问累计时长达5分钟 \\n100:用户近90天访问过的访问内容主题中识别到3个以上含义相近的行业关键词，或者识别到含义相近的行业关键词访问累计时长达10分钟\",\"dimension_status\":\"enabled\",\"dimension_weight\":50},{\"model_name\":\"\",\"name\":\"画像匹配度\",\"dimension_criteria\":\"0: 国家或行业或行业关键词均和目标客户画像匹配不匹配 \\n20:行业、行业关键词、职位、部门、员工人数中有1个和目标客户画像匹配 \\n40:行业、行业关键词、职位、部门、员工人数中有2个和目标客户画像匹配 \\n60:行业、行业关键词、职位、部门、员工人数中有3个和目标客户画像匹配 \\n80:行业、行业关键词、职位、部门、员工人数中有4个和目标客户画像匹配 \\n100:行业、行业关键词、职位、部门、员工人数中5个均和目标客户画像匹配\",\"dimension_status\":\"enabled\",\"dimension_weight\":50}]";

        JSONArray jsonArray = JSONArray.parseArray(jsonData);
        for (Object object : jsonArray) {
            JSONObject jsonObject = (JSONObject) object;
            String name = jsonObject.getString("name");
            if (existNameSet.contains(name)) {
                log.info("initSDRScoringDimensionObj already exist, skip, ea: {} name:{}", ea, name);
                continue;
            }
            String modelName = bantDimensionNameSet.contains(name) ? "BANT" : "画像/意向评分";
            String modelId = modelNameToIdMap.get(modelName);
            if (StringUtils.isBlank(modelId)) {
                log.info("initSDRScoringDimensionObj modelId not found, skip, ea: {} modelName:{}, name:{}", ea, modelName, name);
                continue;
            }
            jsonObject.put("model_name", modelId);
            Map<String, Object> data = Maps.newHashMap();
            data.putAll(jsonObject);
            crmV2Manager.addObjectData(ea, CrmObjectApiNameEnum.SDR_SCORING_DIMENSION_OBJ.getName(), SuperUserConstants.USER_ID, data);
        }


    }

    public void initSDRScoringModelObj(String ea) {
        List<String> nameList = Lists.newArrayList("BANT", "画像/意向评分");
        List<ObjectData> objectDataList  = getObjectListByName(ea, nameList, CrmObjectApiNameEnum.SDR_SCORING_MODEL_OBJ.getName());
        Set<String> existNameSet = objectDataList.stream().map(ObjectData::getName).collect(Collectors.toSet());
        for (String name : nameList) {
            if (existNameSet.contains(name)) {
                log.info("initSDRScoringModelObj already exist, skip, ea: {} name:{}", ea, name);
                continue;
            }
            Map<String, Object> data = new HashMap<>();
            data.put("name", name);
            data.put("model_status", "enabled");
            String description = name.equals("BANT") ? "BANT用于评估客户的需求、预算、决策流程和时间框架" : "用于评估客户画像匹配度和用户行为意向度的综合评分";
            data.put("model_description", description);
            crmV2Manager.addObjectData(ea, CrmObjectApiNameEnum.SDR_SCORING_MODEL_OBJ.getName(), SuperUserConstants.USER_ID, data);
        }

    }

    public List<ObjectData> getObjectListByName(String ea, List<String> modelNameList, String apiName) {
        PaasQueryFilterArg queryFilterArg = new PaasQueryFilterArg();
        queryFilterArg.setObjectAPIName(apiName);
        PaasQueryArg paasQueryArg = new PaasQueryArg(0, 1000);
        queryFilterArg.setQuery(paasQueryArg);
        List<String> selectFields = Lists.newArrayList("_id", "name");
        queryFilterArg.setSelectFields(selectFields);
        paasQueryArg.addFilter("name", "in", modelNameList);
        InnerPage<ObjectData> objectDataPage = crmV2Manager.listCrmObjectByFilterV3(ea, SuperUserConstants.USER_ID, queryFilterArg, 1, modelNameList.size());
        if (objectDataPage != null && CollectionUtils.isNotEmpty(objectDataPage.getDataList())) {
           return objectDataPage.getDataList();
        }
        return Lists.newArrayList();
    }

    public void assignRecord(String ea, String apiName) {
        try {
            FindRoleAndRecordTypeArg arg = new FindRoleAndRecordTypeArg();
            arg.setDescribeApiName(apiName);
            FindRoleAndRecordTypeResult result = fieldDescribeService.findRoleAndRecordType(HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(ea), SuperUserConstants.USER_ID), arg);
            log.info("checkRecordType findRoleAndRecordType ea:{}, apiName:{}, arg:{}, assignResult:{}", ea, apiName, arg, result);
            if (result == null || CollectionUtils.isEmpty(result.getRoleList())) {
                return;
            }
            if (CollectionUtils.isNotEmpty(result.getRoleList()) && result.getRoleList().stream().allMatch(e -> CollectionUtils.isNotEmpty(e.getRecords()))) {
                log.info("checkRecordType already assigned, skip assignRecord, ea:{}, apiName:{}", ea, apiName);
                return;
            }
            String json = "{\"default_record\":\"default__c\",\"roleCode\":\"%s\",\"records\":[\"default__c\"]}";
            List<JSONObject> jsonList = Lists.newArrayList();
            for (FindRoleAndRecordTypeResult.Role role : result.getRoleList()) {
                String roleCode = role.getRoleCode();
                JSONObject jsonObject = JSONObject.parseObject(json);
                jsonObject.put("roleCode", roleCode);
                jsonList.add(jsonObject);
            }
            String assignJson = JSONObject.toJSONString(jsonList);
            AssignRecordArg assignRecordArg = new AssignRecordArg(apiName, assignJson);
            SfaIntegralResult assignResult = fieldDescribeService.assignRecord(HeaderObj.newInstance(eieaConverter.enterpriseAccountToId(ea), SuperUserConstants.USER_ID), assignRecordArg);
            log.info("checkRecordType assignRecord ea:{}, apiName:{}, assignResult:{}", ea, apiName, assignResult);
        } catch (Exception e) {
            log.info("assignRecord error, ea:{}, apiName:{}", ea, apiName, e);
        }
    }

    public void handleWebImVisitorChange(String ea, String webImVisitorId) {
        boolean sdrPluginEnable = marketingPluginConfigManager.getCurrentPluginStatus(ea, MarketingPluginTypeEnum.MARKETING_SDR.getType());
        if (!sdrPluginEnable) {
            return;
        }
        ObjectData webImVisitorObj = crmV2Manager.getObjectData(ea, SuperUserConstants.USER_ID, CrmObjectApiNameEnum.WEB_IM_VISITOR_OBJ.getName(), webImVisitorId);
        if (webImVisitorObj == null) {
            log.info("webImVisitorObj not found, skip, ea:{}, webImVisitorId:{}", ea, webImVisitorId);
            return;
        }
        String marketingVisitorId = webImVisitorObj.getString("marketing_visitor_id");
        if (StringUtils.isBlank(marketingVisitorId)) {
            log.info("webImVisitorObj marketing_visitor_id not found, skip, ea:{}, webImVisitorId:{}", ea, webImVisitorId);
            return;
        }
        // 1 根据访客id查询营销用户
        AssociationArg browserAssociationArg = new AssociationArg();
        browserAssociationArg.setEa(ea);
        browserAssociationArg.setAssociationId(marketingVisitorId);
        browserAssociationArg.setType(ChannelEnum.BROWSER_USER.getType());
        browserAssociationArg.setTriggerSource(ChannelEnum.BROWSER_USER.getDescription());
        browserAssociationArg.setTriggerAction("handleWebImVisitorChange");
        boolean exists = userMarketingAccountRelationManager.isExistsByEaAndKeyProperties(browserAssociationArg);
        if (exists) {
            log.info("browserAssociationArg already exists, skip, ea:{}, marketingVisitorId:{}", ea, marketingVisitorId);
            return;
        }
        AssociationResult associationResult = userMarketingAccountAssociationManager.associate(browserAssociationArg);;
        log.info("handleSdrObject associationResult:{}, ea:{}, marketingVisitorId:{}", associationResult, ea, marketingVisitorId);
    }
}
