package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;
@Data
@Entity
public class DistributeStatusEntity extends BaseEaEntity implements Serializable  {
    /**
     *
     * **/
    private String id;
    /**
     *企业ea
     * **/
    
    /**
     *企业用户ID
     * **/
    private Integer fsUserId;
    /**
     *分销开启状态
     * **/
    private Integer status;
    /**
     *分销创建时间
     * **/
    private Date createTime;
    /**
     *分销更新时间
     * **/
    private Date updateTime;

    /**
     * 线索审批开启状态 0：开启审批 1：关闭审批
     * **/
    private Integer clueAuditStatus;
}
