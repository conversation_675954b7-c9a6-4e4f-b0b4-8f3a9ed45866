package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by zhengh on 2020/12/9.
 */
@Data
public class ShareContentEntity extends BaseEaEntity implements Serializable {
    private String id;
    
    private Integer fsUserId;
    private Integer objectType;
    private String objectId;
    private String title;
    private String description;
    private String image;
    private String thumbnail;
    private Date createTime;
    private Date updateTime;
}
