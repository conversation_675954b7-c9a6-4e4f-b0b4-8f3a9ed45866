package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by wang<PERSON><PERSON> on 2021/12/20 7:25 下午
 */
@Data
public class SpreadChannelEntity extends BaseEaEntity implements Serializable  {

    /**
     * 主键uuid
     */
    private String id;

    /**
     * 公司纷享账号
     */
    

    /**
     * 渠道value
     */
    private String channelValue;

    /**
     * 渠道名称
     */
    private String channelLabel;

    /**
     * 市场活动id
     */
    private String marketingEventId;

    /**
     * 物料类型
     */
    private Integer objectType;

    /**
     * 物料Id
     */
    private String objectId;

    /**
     * h5二维码
     */
    private String h5QrCodeAPath;

    /**
     * 小程序二维码
     */
    private String miniappQrCodeAPath;

    /**
     * 百度二维码
     */
    private String baiduQrCodeAPath;

    /**
     * 短链接
     */
    private String shortUrl;

    /**
     * 长链接
     */
    private String longUrl;

    /**
     * 小程序路径
     */
    private String miniappUrl;

    /**
     * h5url
     */
    private String h5QrUrl;

    /**
     * 小程序二维码url
     */
    private String miniappQrUrl;

    /**
     * 百度二维码url
     */
    private String baiduQrUrl;

    /**
     * 小程序外部链接
     */
    private String miniappForwardUrl;

    /**
     * 网页嵌入代码
     */
    private String webEmbeddedCode;

    /**
     * 公众号的wxAppId
     */
    private String wxAppId;

    /**
     * 微页面id
     */
    private String hexagonSiteId;
}
