package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * Created  By zhoux 2019/10/15
 **/
@Data
public class ImportDataRecordEntity extends BaseEaEntity implements Serializable  {

    private String id;

    

    private Integer importType;

    private String importObjectId;

    private Long importSuccessNum;

    private Long importFailNum;

    private Integer resultType;

    private String resultContent;

    private Integer importUser;

    private Date importTime;

}
