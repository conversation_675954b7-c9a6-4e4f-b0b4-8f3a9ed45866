package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 活动成员（合并表）的支付订单信息
 * <AUTHOR>
 * Created on 2021-01-26.
 */
@Data
public class CampaignPayOrderEntity extends BaseEaEntity implements Serializable  {
	
	private String campaignId;
	private String payOrderId;
	private Date createTime;
	private Date updateTime;
}
