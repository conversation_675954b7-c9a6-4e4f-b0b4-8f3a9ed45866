package com.facishare.marketing.provider.remote.paas.extractor;

import com.facishare.marketing.common.util.GsonUtil;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import java.lang.reflect.Type;

/**
 * <AUTHOR>
 */
public abstract class AbstractDataExtractor implements DataExtractor {
    private final Gson innerGson = new Gson();

    @Override
    public final <T> T extract(String body, Type type) {
        Gson gson = getGsonResolver();
        JsonElement jsonElement = extractRealData(GsonUtil.parseToJsonObject(body));
        return gson.fromJson(jsonElement, type);
    }

    /**
     * 字符串提取出实际数据作为JsonElement
     */
    abstract JsonElement extractRealData(JsonElement jsonElement);

    protected Gson getGsonResolver() {
        return innerGson;
    }
}
