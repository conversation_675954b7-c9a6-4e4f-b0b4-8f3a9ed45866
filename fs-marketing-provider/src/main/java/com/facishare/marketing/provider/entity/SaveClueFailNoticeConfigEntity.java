package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import com.facishare.marketing.common.typehandlers.value.safetyManagement.SafetyManagementSetting;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SaveClueFailNoticeConfigEntity extends BaseEaEntity implements Serializable  {
    private String id;

    

    private Integer clueType;

    private Integer sendType;

    private Integer receiverType;

    private Date createTime;

    private Date updateTime;

    private Date timingTime;

    private String sendScope;
}
