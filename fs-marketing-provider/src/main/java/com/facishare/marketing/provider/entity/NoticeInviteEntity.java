package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Entity;
import lombok.Data;

@Entity
@Data
public class NoticeInviteEntity extends BaseEaEntity implements Serializable  {
    /** id **/
    private String id;
    /** 通知标题 **/
    private String title;
    /** 内容id **/
    private String content;
    /** 发送时间 **/
    private Date sendTime;
    /** 发送状态 1：未发送 2：发送成功 3：发送失败 **/
    private Integer status;
    /** 创建时间 **/
    private Date createTime;
    /** 发送范围 **/
    private String sendScope;
    /** 操作人的纷享企业账号 **/
    
    /** 操作人的纷享用户id **/
    private Integer fsUserId;
}
