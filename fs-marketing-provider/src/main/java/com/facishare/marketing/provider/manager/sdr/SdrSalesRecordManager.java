package com.facishare.marketing.provider.manager.sdr;

import com.alibaba.fastjson.JSONObject;
import com.facishare.ai.api.dto.BaseArgument;
import com.facishare.ai.api.dto.PromptCompletions;
import com.facishare.ai.api.model.service.FsAI;
import com.facishare.converter.EIEAConverter;
import com.facishare.marketing.common.contstant.CrmStatusMessageConstant;
import com.facishare.marketing.common.enums.crm.CrmObjectApiNameEnum;
import com.facishare.marketing.common.enums.leads.ClueDefaultSettingTypeEnum;
import com.facishare.marketing.outapi.service.ClueDefaultSettingService;
import com.facishare.marketing.provider.manager.crmobjectcreator.MarketingLeadSyncRecordObjManager;
import com.facishare.marketing.provider.manager.permission.DataPermissionManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.marketing.provider.remote.paas.crm.vo.CrmUserDefineFieldVo;
import com.facishare.marketing.provider.util.i18n.I18NUtil;
import com.fxiaoke.crmrestapi.common.contants.ObjectDescribeContants;
import com.fxiaoke.crmrestapi.common.contants.SuperUserConstants;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class SdrSalesRecordManager {

    @Autowired
    private EIEAConverter converter;
    @Autowired
    private DataPermissionManager dataPermissionManager;
    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private ClueDefaultSettingService clueDefaultSettingService;
    @Autowired
    private MarketingLeadSyncRecordObjManager marketingLeadSyncRecordObjManager;

    /**
     * 销售记录生成
     *
     * @param ea
     * @param chatRecords
     * @param customerServiceSessionId
     * @return
     */
    public void salesRecordGeneration(String ea, String chatRecords, String customerServiceSessionId) {
        // 优化日志记录
        if (StringUtils.isBlank(chatRecords)) {
            return;
        }
        try {
            Map<String, String> sceneVariables = Maps.newHashMap();
            sceneVariables.put("chatRecords", chatRecords);
            List<CrmUserDefineFieldVo> objectFieldDescribesList = crmV2Manager.getObjectFieldDescribesList(ea, CrmObjectApiNameEnum.CRM_LEAD);
            sceneVariables.put("leadsFields", JSONObject.toJSONString(objectFieldDescribesList));
            BaseArgument context = new BaseArgument();
            context.setTenantId(String.valueOf(converter.enterpriseAccountToId(ea)));
            context.setUserId(String.valueOf(SuperUserConstants.USER_ID));
            context.setLocale(I18NUtil.getLanguage());
            context.setBusiness(SdrActionManager.BIZ_NAME);
            PromptCompletions.Arg promptArg = new PromptCompletions.Arg();
            promptArg.setApiName("prompt_SDR_sales_record");
            promptArg.setSceneVariables(sceneVariables);
            PromptCompletions.Result promptResult = FsAI.prompt().completions(context, promptArg);
            log.info("salesRecordGeneration ea:{} promptCompletionsArg={} result:{}", ea, JSONObject.toJSON(promptArg), promptResult);
            String jsonResult = promptResult.getMessage();
            if (StringUtils.isNotBlank(jsonResult)) {
                JSONObject objectData = JSONObject.parseObject(jsonResult);
                objectData.put("related_api_names", Lists.newArrayList("CustomerServiceSessionObj"));
                objectData.put("related_object_data", Lists.newArrayList(Maps.newHashMap(ImmutableMap.of("describe_api_name", "CustomerServiceSessionObj", "id", customerServiceSessionId))));
                objectData.put("interaction_channels", "ocs");
                objectData.put("interactive_types", "chat");
                this.saveSalesRecordObj(ea, objectData);
            }
        } catch (Exception e) {
            log.error("SdrLeadsSyncManager salesRecordGeneration error", e);
        }
    }

    public void saveSalesRecordObj(String fsEa, Map<String, Object> dataMap) {
        if (dataMap == null) {
            return;
        }
        ObjectData objectData = new ObjectData();
        objectData.putAll(dataMap);
        objectData.put(ObjectDescribeContants.DESCRIBE_API_NAME, "ActiveRecordObj");
        objectData.put(ObjectDescribeContants.OBJECT_DESCRIBE_ID, "ActiveRecordObj");

        // 获取线索创建人
        Integer createBy = clueDefaultSettingService.getClueCreator(null, fsEa, ClueDefaultSettingTypeEnum.OTHER.getType());
        Map<String, List<String>> orgMap = dataPermissionManager.getDataOwnerOrganizationByUserIds(fsEa, Collections.singletonList(createBy));
        List<String> org = orgMap.get(String.valueOf(objectData.getOwner()));
        if (org != null) {
            objectData.put("data_own_organization", org);
        } else {
            objectData.put("data_own_organization", Lists.newArrayList("999999"));
        }
        // 创建销售记录
        com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> actionAddResultResult = crmV2Manager.addObjectData(fsEa, "ActiveRecordObj", createBy, objectData);
        if (actionAddResultResult != null && actionAddResultResult.getCode() == com.fxiaoke.crmrestapi.common.result.Result.SUCCESS_CODE) {
            String leadId = actionAddResultResult.getData().getObjectData().getId();
            log.info("SdrSalesRecordManager saveSalesRecordObj success data:{} leadId:{}", objectData, leadId);
        } else {
            String message = CrmStatusMessageConstant.SYSTEM_ERROR;
            if (actionAddResultResult != null && StringUtils.isNotBlank(actionAddResultResult.getMessage())) {
                message = actionAddResultResult.getMessage();
            }
            log.info("SdrSalesRecordManager saveSalesRecordObj failed data:{} message:{}", objectData, message);
        }
    }

}
