package com.facishare.marketing.provider.remote.paas.crm.searchquery;

import com.google.common.base.Strings;
import java.util.HashMap;

/**
 * 模糊查询,目前仅支持前匹配 Created by zhangk on 2016/7/1.
 */
@SuppressWarnings("unchecked")
public class MatchConditions extends AbstractConditions<String> implements IConditions<HashMap<String, String>> {
    public MatchConditions() {
        setType(IConditions.MATCH_CONDITION);
    }

    /**
     * 添加查询条件
     *
     * @param fieldName 字段名称
     * @param MatchType 匹配类型
     * @param value 查询值
     */
    public void addCondition(String fieldName, String MatchType, String value) {
        if (!Strings.isNullOrEmpty(MatchType)) {
            addCondition(fieldName + "." + MatchType, value);
        } else {
            addCondition(fieldName, value);
        }
    }
}