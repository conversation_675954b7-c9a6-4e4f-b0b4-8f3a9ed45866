package com.facishare.marketing.provider.entity.enterpriseFeed;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by ranluch on 2019/1/15.
 */
@Data
@Entity
public class EnterpriseFeedEntity extends BaseEaEntity implements Serializable  {
    /**
     * 主键uuid
     */
    private String id;

    /**
     * 公司纷享账号
     */
    

    /**
     * 物料的唯一性标识
     */
    private String objectId;

    /**
     * 物料的类型
     */
    private Integer objectType;

    /**
     * 推荐语
     */
    private String recommendation;

    /**
     * 公司纷享user id
     */
    private Integer userId;

    /**
     * 动态的状态
     */
    private int status;

    /**
     * 互动数
     */
    private int activeCount;

    /**
     * 浏览数
     */
    private int lookUpCount;

    /**
     * 转发数
     */
    private int forwardCount;

    private Date createTime;

    private Date updateTime;
}
