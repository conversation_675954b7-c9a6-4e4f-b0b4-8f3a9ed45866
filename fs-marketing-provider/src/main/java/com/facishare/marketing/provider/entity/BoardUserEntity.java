package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2020/8/5 11:43
 * @Version 1.0
 */
@Data
public class BoardUserEntity extends BaseEaEntity implements Serializable  {
    private String boardId;
    
    private Integer employeeId;//员工的id
    private Date createTime;
    private Date updateTime;
}
