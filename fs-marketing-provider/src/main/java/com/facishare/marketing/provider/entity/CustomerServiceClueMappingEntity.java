package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import com.facishare.marketing.common.typehandlers.value.FieldMappings;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CustomerServiceClueMappingEntity extends BaseEaEntity implements Serializable  {
    private String id;

    

    private FieldMappings customerServiceClueMapping;

    private Date createTime;

    private Date updateTime;

}
