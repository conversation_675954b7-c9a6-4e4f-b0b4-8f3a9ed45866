package com.facishare.marketing.provider.remote.rest.arg;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

@Data
public class FindLayoutListArg implements Serializable {

    @SerializedName("objectDescribeApiName")
    private String objectDescribeApiName;

    // 列表页: list_layout
    @SerializedName("layoutType")
    private String layoutType;
}
