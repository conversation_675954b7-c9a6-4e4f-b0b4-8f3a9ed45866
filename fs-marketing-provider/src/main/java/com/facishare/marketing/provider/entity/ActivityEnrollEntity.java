package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import com.facishare.marketing.common.enums.conference.ConferenceEnrollSourceTypeEnum;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ActivityEnrollEntity extends BaseEaEntity implements Serializable  {
    private static final long serialVersionUID = 1L;
    private Integer spreadUserId;
    private String id;
    /** {@link ConferenceEnrollSourceTypeEnum} */
    private int enrollSourceType;
    private String uid;
    private String activityId;
    private String name;
    private String phone;
    private String email;
    private String companyName;
    private String position;
    private String text1;
    private String text2;
    private String text3;
    private String text4;
    private String text5;
    private String text6;
    private String text7;
    private BigDecimal num1;
    private BigDecimal num2;
    private List<String> texts1;
    /** 同步crm状态 */
    private Integer leadSaveStatus;
    private String leadId;
    private String leadMessage;
    private Date createTime;
    private Integer signIn;
}
