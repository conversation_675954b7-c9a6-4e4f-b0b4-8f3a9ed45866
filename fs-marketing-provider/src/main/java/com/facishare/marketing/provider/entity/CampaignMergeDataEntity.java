package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.facishare.marketing.common.enums.campaign.CampaignMergeDataObjectTypeEnum;
import com.facishare.marketing.common.model.SmsParamObject;
import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * Created  By zhoux 2020/10/14
 **/
@Data
public class CampaignMergeDataEntity extends BaseEaEntity implements Serializable , SmsParamObject  {

    private String id;

    

    private String marketingEventId;

    private String campaignMembersObjId;

    private Integer bindCrmObjectType;

    private String bindCrmObjectId;

    private String name;

    private String phone;

    private Integer inviteStatus;

    private Date createTime;

    private Date updateTime;

    private Integer sourceType;

    private Boolean addCampaignMember; //标记是否为添加的参与成员

    /**
     * 外部平台用户id
     */
    private String outerUserId;

    private Long totalAmount;

    // 营销推广来源ID 不会持久化
    private String marketingPromotionSourceId;

    public boolean phoneNotNull () {
        if (StringUtils.isNotEmpty(phone)) {
            return true;
        }
        return false;
    }

    public boolean bindCrmObjectTypeAndBindCrmObjectIdValid () {
        if (CampaignMergeDataObjectTypeEnum.isTypeValid(bindCrmObjectType) && StringUtils.isNotEmpty(bindCrmObjectId)) {
            return true;
        }
        return false;
    }

    @Override
    public Map<String, String> getParamDescMap() {
        Map<String, String> paramDescMap = new HashMap<>();
        paramDescMap.put("enroll.name", I18nUtil.get(I18nKeyEnum.MARK_ENTITY_CAMPAIGNMERGEDATAENTITY_70));
//        paramDescMap.put("enroll.email", "邮箱");
        paramDescMap.put("enroll.phone", I18nUtil.get(I18nKeyEnum.MARK_ENTITY_CAMPAIGNMERGEDATAENTITY_72));
        return paramDescMap;
    }

    @Override
    public Map<String, String> getParamValueMap() {
        Map<String, String> paramValueMap = new HashMap<>();
        paramValueMap.put("enroll.name", this.getName());
//        paramValueMap.put("enroll.email", "");
        paramValueMap.put("enroll.phone", this.getPhone());
        return paramValueMap;
    }
}