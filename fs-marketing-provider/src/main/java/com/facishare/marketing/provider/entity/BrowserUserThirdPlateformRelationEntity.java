package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class BrowserUserThirdPlateformRelationEntity extends BaseEaEntity implements Serializable  {
    private String id;
    
    private String browserUserId;
    private String thirdUserId;
    private Integer type;
    private Date createTime;
    private Date updateTime;
    private String landPage;

    private String leadId;

    private String mobile;
}
