package com.facishare.marketing.provider.remote.paas.crm.vo;

import com.facishare.marketing.api.result.CrmFieldResult;
import com.facishare.marketing.api.result.EnumDetailResult;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableSet;
import java.io.Serializable;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

/**
 * Created by ligang on 2017/2/21.
 */
@Data
public class CrmUserDefineFieldVo implements Serializable {
    private static final long serialVersionUID = 1L;
    //字段属性：1、系统预置字段；2、用户自定义字段
    private Integer fieldProperty;
    private String fieldName;
    private String fieldCaption;
    //字段类型：1、分割线；2、单行文本；3、多行文本；4、整数；5、小数；6、金额；7、日期时间；8、单选；9、多选；10、图像；11、地址；12、生日；13、布尔型；14、级联单选；15、日期；16、单选选人控件;17、附件类型；18、电话；19、邮件
    private Integer fieldType;
    private String fieldTypeName;
    private Boolean isNotNull;
    private List<EnumDetailResult> enumDetails;
    //查找关联字段关联的targetApiName;
    private String targetApiName;
    private String defineType;

    public String getEnumLabel(String enumKey){
        if (enumDetails != null){
            for (EnumDetailResult enumDetail : enumDetails) {
                if (enumDetail.getItemCode().equals(enumKey)){
                    return enumDetail.getItemName();
                }
            }
        }
        return null;
    }

    public static List<CrmFieldResult> transferCrmUserDefineFieldVoListToCrmFieldVoList(List<CrmUserDefineFieldVo> crmCustomerFields) {
        return crmCustomerFields.stream().map(crmUserDefineFieldVo -> {
            CrmFieldResult crmFieldResult = new CrmFieldResult();
            BeanUtils.copyProperties(crmUserDefineFieldVo, crmFieldResult);
            return crmFieldResult;
        }).collect(Collectors.toList());
    }

    public boolean isValidField() {
        return fieldType != null && fieldType != FieldType.DIVIDING_LINE && !Strings.isNullOrEmpty(fieldName);
    }


    public static class FieldType {
        public static final int DIVIDING_LINE = 1;
        public static final int SINGLE_LINE_TEXT = 2;
        public static final int MULTIPLE_LINE_TEXT = 3;
        public static final int INTEGER = 4;
        public static final int FLOAT = 5;
        public static final int CURRENCY = 6;
        public static final int DATETIME = 7;
        public static final int SINGLE_SELECT = 8;
        public static final int MULTIPLE_SELECT = 9;
        public static final int IMAGE = 10;
        public static final int ADDRESS = 11;
        public static final int BIRTHDAY = 12;
        public static final int BOOLEAN = 13;
        public static final int CASCADE_SINGLE_SELECT = 14;
        public static final int DATE = 15;
        public static final int LOOKUP_ASSOCIATION_ = 16;
        public static final int ATTACHMENT = 17;
        public static final int PHONE = 18;
        public static final int EMAIL = 19;
        public static final Set<Integer> ENUM_TYPE = ImmutableSet.of(SINGLE_SELECT, MULTIPLE_SELECT, CASCADE_SINGLE_SELECT);
    }


}
