package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * Created on 2021-02-23.
 */
@Data
public class MarketingSceneEntity extends BaseEaEntity implements Serializable  {
	private String id;
	
	/** @see com.facishare.marketing.common.enums.MarketingSceneType*/
	private String sceneType;
	private String targetId;
	private Date createTime;
	private Date updateTime;
}
