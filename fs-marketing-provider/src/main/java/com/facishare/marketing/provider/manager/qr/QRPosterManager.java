package com.facishare.marketing.provider.manager.qr;

import com.facishare.marketing.common.enums.I18nKeyEnum;
import com.facishare.marketing.common.util.I18nUtil;
import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.facishare.marketing.api.data.material.QRPosterBriefData;
import com.facishare.marketing.api.result.qr.QueryQRPosterByEaListUnitResult;
import com.facishare.marketing.common.contstant.OperatorConstants;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.qr.QRPosterForwardTypeEnum;
import com.facishare.marketing.common.enums.qr.QRPosterStatusEnum;
import com.facishare.marketing.common.enums.qr.QRPosterTypeEnum;
import com.facishare.marketing.common.result.Result;
import com.facishare.marketing.common.result.SHErrorCode;
import com.facishare.marketing.common.util.EmptyUtil;
import com.facishare.marketing.common.util.GsonUtil;
import com.facishare.marketing.common.util.UUIDUtil;
import com.facishare.marketing.provider.dao.*;
import com.facishare.marketing.provider.dao.conference.ConferenceInvitationDAO;
import com.facishare.marketing.provider.dao.qr.QRCodeDAO;
import com.facishare.marketing.provider.dao.qr.QRPosterDAO;
import com.facishare.marketing.provider.entity.*;
import com.facishare.marketing.provider.entity.conference.ConferenceInvitationEntity;
import com.facishare.marketing.provider.entity.qr.QRCodeEntity;
import com.facishare.marketing.provider.entity.qr.QRPosterEntity;
import com.facishare.marketing.provider.innerArg.CreateQRPosterArg;
import com.facishare.marketing.provider.manager.CustomizeFormDataManager;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.MarketingEventManager;
import com.facishare.marketing.provider.manager.objecttop.ObjectTopManager;
import com.facishare.marketing.provider.remote.CrmV2Manager;
import com.facishare.wechat.proxy.common.result.ModelResult;
import com.facishare.wechat.proxy.model.result.QrCodeResult;
import com.facishare.wechat.union.core.api.model.arg.BatchGetByEaAndWxAppIdsArg;
import com.facishare.wechat.union.core.api.model.result.BatchGetByWxAppIdsResult;
import com.facishare.wechat.union.core.api.model.vo.OuterServiceWechatVO;
import com.fxiaoke.crmrestapi.common.contants.LeadsFieldContants;
import com.fxiaoke.crmrestapi.common.contants.MarketingEventFieldContants;
import com.fxiaoke.crmrestapi.common.data.Filter;
import com.fxiaoke.crmrestapi.common.data.MarketingEventData;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.SearchQuery;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class QRPosterManager {

    @Autowired
    private ProductDAO productDAO;
    @Autowired
    private ArticleDAO articleDAO;
    @Autowired
    private CustomizeFormDataDAO customizeFormDataDAO;
    @Autowired
    private ActivityDAO activityDAO;
    @Autowired
    private ConferenceInvitationDAO conferenceInvitationDAO;
    @Autowired
    private QRCodeDAO qrCodeDAO;

    @Autowired
    private com.facishare.wechat.proxy.service.QrCodeService qrCodeService;
    @Autowired
    private com.facishare.wechat.union.core.api.service.OuterServiceWechatService outerServiceWechatService;

    @Autowired
    private QRPosterDAO qrPosterDAO;
    @Autowired
    private ContentMarketingEventMaterialRelationDAO contentMarketingEventMaterialRelationDAO;

    @Autowired
    private CrmV2Manager crmV2Manager;
    @Autowired
    private FileV2Manager fileV2Manager;
    @Autowired
    private CustomizeFormDataManager customizeFormDataManager;
    @Autowired
    private MarketingEventManager marketingEventManager;

    @Autowired
    private ObjectTopManager objectTopManager;
    @ReloadableProperty("host")
    private String host;

    private Set<String> productIdSet = new HashSet<>();
    private Set<String> articleIdSet = new HashSet<>();
    private Set<String> customizeFormIdSet = new HashSet<>();
    private Set<String> activityIdSet = new HashSet<>();
    private Set<String> conferenceInvitationIdSet = new HashSet<>();
    private Set<Long> wxOfficialAccountQRIdSet = new HashSet<>();
    private Set<Integer> qrCodeIdSet = new HashSet<>();
    private Set<String> wxOfficialAccountIdSet = new HashSet<>();
    private Set<String> conferenceFormIdSet = new HashSet<>();

    private Map<String, MarketingEventData> marketingEventDataMap = new HashMap<>();
    private Map<String, ProductEntity> productEntityMap = new HashMap<>();
    private Map<String, ArticleEntity> articleEntityMap = new HashMap<>();
    private Map<String, CustomizeFormDataEntity> customizeFormEntityMap = new HashMap<>();
    private Map<String, ActivityEntity> activityEntityMap = new HashMap<>();
    private Map<String, ConferenceInvitationEntity> conferenceInvitationEntityMap = new HashMap<>();
    private Map<Integer, QRCodeEntity> qrCodeEntityMap = new HashMap<>();
    private Map<Long, QrCodeResult> wxQRCodeMap = new HashMap<>();
    private Map<String, BatchGetByWxAppIdsResult> wxOfficialAccountMap = new HashMap<>();
    private Map<String, CustomizeFormDataEntity> conferenceFormEntityMap = new HashMap<>();

    
    public QRPosterEntity createQRPoster(CreateQRPosterArg createQRPosterArg) {
        if (EmptyUtil.isNullForList(createQRPosterArg.getEa(), createQRPosterArg.getUserId(), createQRPosterArg.getMarketingEventId(),
                createQRPosterArg.getForwardType(), createQRPosterArg.getTitle(), createQRPosterArg.getBgAPath(), createQRPosterArg.getFinalAPath(),
                createQRPosterArg.getQrStyle(), createQRPosterArg.getType())) {
            log.warn("QRPosterManager.createQRPoster params error the first");
            return null;
        }

        if (QRPosterForwardTypeEnum.hasTargetId(createQRPosterArg.getForwardType()) && EmptyUtil.isNullForList(createQRPosterArg.getTargetId())) {
            log.warn("QRPosterManager.createQRPoster params error the second");
            return null;
        }

        if (QRPosterForwardTypeEnum.needCreateQRCode(createQRPosterArg.getForwardType())) {
            if (EmptyUtil.isNullForList(createQRPosterArg.getQrCodeId())) {
                log.warn("QRPosterManager.createQRPoster params error the third");
                return null;
            }
        } else if (QRPosterForwardTypeEnum.isExternalQRUrl(createQRPosterArg.getForwardType())) {
            if (EmptyUtil.isNullForList(createQRPosterArg.getQrCodeUrl())) {
                log.warn("QRPosterManager.createQRPoster params error the fourth");
                return null;
            }
        }

        if (createQRPosterArg.getForwardType() == QRPosterForwardTypeEnum.WX_OFFICIAL_ACCOUNT && EmptyUtil.isNull(createQRPosterArg.getWxAppId())) {
            log.warn("QRPosterManager.createQRPoster params error the fifth");
            return null;
        }
        if (createQRPosterArg.getForwardType() == QRPosterForwardTypeEnum.WX_OFFICIAL_ACCOUNT_CHANNEL_QR_CODE && ((EmptyUtil.isNull(createQRPosterArg.getWxAppId())) || Strings.isNullOrEmpty(createQRPosterArg.getTargetId()))) {
            log.warn("QRPosterManager.createQRPoster params error the sixth");
            return null;
        }
        

        boolean needUpdate = false;
        QRPosterEntity qrPosterEntity = new QRPosterEntity();
        if (StringUtils.isNotEmpty(createQRPosterArg.getQrPostId())) {
            QRPosterEntity queryQRPosterEntity = qrPosterDAO.queryById(createQRPosterArg.getQrPostId());
            if (null == queryQRPosterEntity) {
                log.warn("QRPosterManager.createQRPoster queryQRPosterEntity not found, id = {}", createQRPosterArg.getQrPostId());
                return null;
            }
            if (!queryQRPosterEntity.getEa().equals(createQRPosterArg.getEa())) {
                log.warn("QRPosterManager.createQRPoster ea invaild, id = {}, ea = {}", createQRPosterArg.getQrPostId(), createQRPosterArg.getEa());
                return null;
            }
            if (queryQRPosterEntity.getStatus() != QRPosterStatusEnum.NORMAL.getStatus()) {
                log.warn("QRPosterManager.createQRPoster has been deleted, id = {}", createQRPosterArg.getQrPostId());
                return null;
            }

            qrPosterEntity.setId(createQRPosterArg.getQrPostId());
            needUpdate = true;
        } else {
            qrPosterEntity.setId(UUIDUtil.getUUID());
        }

        qrPosterEntity.setMarketingEventId(createQRPosterArg.getMarketingEventId());
        qrPosterEntity.setForwardType(createQRPosterArg.getForwardType().getType());
        qrPosterEntity.setType(createQRPosterArg.getType().getType());

        if (QRPosterForwardTypeEnum.needCreateQRCode(createQRPosterArg.getForwardType())) {
            qrPosterEntity.setQrCodeId(createQRPosterArg.getQrCodeId());
        } else if (QRPosterForwardTypeEnum.isExternalQRUrl(createQRPosterArg.getForwardType())) {
            qrPosterEntity.setExternalQrUrl(createQRPosterArg.getQrCodeUrl());
        }

        if ((createQRPosterArg.getForwardType() == QRPosterForwardTypeEnum.WX_OFFICIAL_ACCOUNT || createQRPosterArg.getForwardType() == QRPosterForwardTypeEnum.WX_OFFICIAL_ACCOUNT_CHANNEL_QR_CODE) && !EmptyUtil.isNull(createQRPosterArg.getWxAppId())) {
            String newTargetId = createQRPosterArg.getWxAppId() + "," + createQRPosterArg.getTargetId();
            qrPosterEntity.setTargetId(newTargetId);
        } else {
            qrPosterEntity.setTargetId(createQRPosterArg.getTargetId());
        }

        qrPosterEntity.setStatus(QRPosterStatusEnum.NORMAL.getStatus());
        qrPosterEntity.setEa(createQRPosterArg.getEa());
        qrPosterEntity.setTitle(createQRPosterArg.getTitle());
        qrPosterEntity.setBgApath(createQRPosterArg.getBgAPath());
        qrPosterEntity.setBgThumbnailApath(createQRPosterArg.getBgThumbnailApath());
        qrPosterEntity.setApath(createQRPosterArg.getFinalAPath());
        qrPosterEntity.setThumbnailApath(createQRPosterArg.getFinalThumbnailApath());
        qrPosterEntity.setQrStyle(createQRPosterArg.getQrStyle());
        qrPosterEntity.setCreateBy(createQRPosterArg.getUserId());
        qrPosterEntity.setCreateTime(new Date());
        qrPosterEntity.setUpdateTime(new Date());
        qrPosterEntity.setPosterStyle(createQRPosterArg.getPosterStyle());
        qrPosterEntity.setUserAddSettings(createQRPosterArg.getUserAddSettings());
        qrPosterEntity.setFailedOperation(createQRPosterArg.getFailedOperation());
        if (needUpdate) {
            if (qrPosterDAO.update(qrPosterEntity) != 1) {
                log.error("QRPosterManager.createQRPoster qrPosterDAO.update failed, entity={}", qrPosterEntity);
                return null;
            }
        } else {
            qrPosterEntity.setIsMobileDisplay(false);
            if (qrPosterDAO.insert(qrPosterEntity) != 1) {
                log.error("QRPosterManager.createQRPoster qrPosterDAO.insert failed, entity={}", qrPosterEntity);
                return null;
            }
        }

        com.facishare.marketing.api.data.MarketingEventData marketingEventData = marketingEventManager.getMarketingEventData(createQRPosterArg.getEa(), -10000, createQRPosterArg.getMarketingEventId());
        if (marketingEventData == null){
            return null;
        }
        ContentMarketingEventMaterialRelationEntity queryContentMarketingEventMaterialRelationEntity = contentMarketingEventMaterialRelationDAO.getByMarketingEventIdAndObjectTypeAndObjectId(createQRPosterArg.getEa(), createQRPosterArg.getMarketingEventId(), ObjectTypeEnum.QR_POSTER.getType(), qrPosterEntity.getId());
        if (null == queryContentMarketingEventMaterialRelationEntity ) {
            ContentMarketingEventMaterialRelationEntity contentMarketingEventMaterialRelationEntity = new ContentMarketingEventMaterialRelationEntity();
            contentMarketingEventMaterialRelationEntity.setId(UUIDUtil.getUUID());
            contentMarketingEventMaterialRelationEntity.setEa(createQRPosterArg.getEa());
            contentMarketingEventMaterialRelationEntity.setMarketingEventId(createQRPosterArg.getMarketingEventId());
            contentMarketingEventMaterialRelationEntity.setObjectType(ObjectTypeEnum.QR_POSTER.getType());
            contentMarketingEventMaterialRelationEntity.setObjectId(qrPosterEntity.getId());
            marketingEventData.setEventType(marketingEventData.getEventType());
            contentMarketingEventMaterialRelationDAO.save(contentMarketingEventMaterialRelationEntity);
        }

        return qrPosterEntity;
    }

    public boolean deleteQRPoster(String qrPosterId) {
        if (EmptyUtil.isNull(qrPosterId)) {
            log.warn("QRPosterManager.deleteQRPoster params error");
            return false;
        }

        qrPosterDAO.deleteById(qrPosterId);
        contentMarketingEventMaterialRelationDAO.deleteByObjectTypeAndObjectId(ObjectTypeEnum.QR_POSTER.getType(), qrPosterId);
        //删除置顶数据
        objectTopManager.deleteByObjectIdAndObjectType(ObjectTypeEnum.QR_POSTER.getType(), Collections.singletonList(qrPosterId));
        return true;
    }

    public void deleteQRPosterBatch(List<String> qrPosterIdList) {
        qrPosterDAO.deleteByIdList(qrPosterIdList);
        contentMarketingEventMaterialRelationDAO.deleteByObjectTypeAndObjectIdList(ObjectTypeEnum.QR_POSTER.getType(), qrPosterIdList);
    }

    public void batchQueryMarketingEventInfo(String ea, List<String> marketingEventIdList) {
        if (CollectionUtils.isEmpty(marketingEventIdList)) {
            return ;
        }
        int totalCount = marketingEventIdList.size();
        int pageSize = 200;
        int totalPage =  totalCount % pageSize == 0 ? totalCount / pageSize : totalCount / pageSize + 1;
        for (int i = 0; i < totalPage; i++) {
            List<String> queryIdList = marketingEventIdList.stream().skip(i * pageSize).limit(pageSize).collect(Collectors.toList());
            SearchQuery searchQuery = new SearchQuery();
            searchQuery.setLimit(pageSize);
            searchQuery.setOffset(i * pageSize);
            searchQuery.addFilter("_id", queryIdList, OperatorConstants.IN);
            com.fxiaoke.crmrestapi.common.data.Page<ObjectData> crmMarketingEventResult = crmV2Manager.getList(ea, -10000, MarketingEventFieldContants.API_NAME, searchQuery);
            List<ObjectData> dataList = crmMarketingEventResult.getDataList();
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(dataList)) {
                for (ObjectData objectData : dataList) {
                    MarketingEventData marketingEventVO = MarketingEventData.wrap(objectData);
                    marketingEventDataMap.put(marketingEventVO.getId(), marketingEventVO);
                }
            }
        }
    }

    public void batchGetMaterialByIds(String ea, List<? extends QRPosterEntity> qrPosterEntityList) {
        qrPosterEntityList.forEach(this::getMaterialIdSet);
        // 产品
        if (CollectionUtils.isNotEmpty(productIdSet)) {
            List<ProductEntity> productEntityList = productDAO.getByIds(new ArrayList<>(productIdSet));
            for (ProductEntity productEntity : productEntityList) {
                productEntityMap.put(productEntity.getId(), productEntity);
            }
        }

        // 文章
        if (CollectionUtils.isNotEmpty(articleIdSet)) {
            List<ArticleEntity> articleEntityList = articleDAO.getByIds(new ArrayList<>(articleIdSet));
            for (ArticleEntity articleEntity : articleEntityList) {
                articleEntityMap.put(articleEntity.getId(), articleEntity);
            }
        }

        // 表单
        if (CollectionUtils.isNotEmpty(customizeFormIdSet)) {
            List<CustomizeFormDataEntity> customizeFormEntityList = customizeFormDataDAO.queryCustomizeFormDataEntityListByIds(new ArrayList<>(customizeFormIdSet));
            for (CustomizeFormDataEntity customizeFormDataEntity : customizeFormEntityList) {
                customizeFormEntityMap.put(customizeFormDataEntity.getId(), customizeFormDataEntity);
            }
        }

        // 会议详情
        if (CollectionUtils.isNotEmpty(activityIdSet)) {
            List<ActivityEntity> activityEntityList = activityDAO.getByIds(new ArrayList<>(activityIdSet));
            for (ActivityEntity activityEntity : activityEntityList) {
                activityEntityMap.put(activityEntity.getId(), activityEntity);
            }
        }

        // 会议邀请函
        if (CollectionUtils.isNotEmpty(conferenceInvitationIdSet)) {
            List<ConferenceInvitationEntity> conferenceInvitationEntityList = conferenceInvitationDAO.getInvitationByIds(new ArrayList<>(conferenceInvitationIdSet));
            for (ConferenceInvitationEntity conferenceInvitationEntity : conferenceInvitationEntityList) {
                conferenceInvitationEntityMap.put(conferenceInvitationEntity.getId(), conferenceInvitationEntity);
            }
        }

        // 二维码信息
        List<QRCodeEntity> qrCodeEntityList = null;
        if (CollectionUtils.isNotEmpty(qrCodeIdSet)) {
            qrCodeEntityList = qrCodeDAO.queryByIds(new ArrayList<>(qrCodeIdSet));
            for (QRCodeEntity qrCodeEntity : qrCodeEntityList) {
                qrCodeEntityMap.put(qrCodeEntity.getId(), qrCodeEntity);
            }
        }

        // 渠道二维码信息
        if (CollectionUtils.isNotEmpty(wxOfficialAccountQRIdSet)) {
            ModelResult<List<QrCodeResult>> modelResult = qrCodeService.queryQrCodeByIds(ea, new ArrayList<>(wxOfficialAccountQRIdSet));
            if (modelResult.isSuccess()) {
                List<QrCodeResult> results = modelResult.getResult();
                for (QrCodeResult qrCodeResult : results) {
                    wxQRCodeMap.put(qrCodeResult.getId(), qrCodeResult);
                }
            }
        }

        // 公众号信息
        if (CollectionUtils.isNotEmpty(wxOfficialAccountIdSet)) {

            ModelResult<List<BatchGetByWxAppIdsResult>> modelResult = outerServiceWechatService.batchGetByWxAppIds(new ArrayList<>(wxOfficialAccountIdSet));
            if (modelResult.isSuccess()) {
                List<BatchGetByWxAppIdsResult> results = modelResult.getResult();
                for (BatchGetByWxAppIdsResult batchGetByWxAppIdsResult : results) {
                    wxOfficialAccountMap.put(batchGetByWxAppIdsResult.getWxAppId(), batchGetByWxAppIdsResult);
                }
            }
        }

        // 会议表单
        if (CollectionUtils.isNotEmpty(conferenceFormIdSet)) {
            conferenceFormEntityMap = customizeFormDataManager.getBindFormDataByObjects(ea, new ArrayList<>(conferenceFormIdSet));
        }
    }

    public void getMaterialIdSet(QRPosterEntity qrPosterEntity) {
        QRPosterForwardTypeEnum qrPosterForwardTypeEnum = QRPosterForwardTypeEnum.getByType(qrPosterEntity.getForwardType());
        if (qrPosterForwardTypeEnum != null) {
            if (null != qrPosterEntity.getQrCodeId()) {
                qrCodeIdSet.add(qrPosterEntity.getQrCodeId());
            }

            if (qrPosterForwardTypeEnum == QRPosterForwardTypeEnum.PRODUCT) {
                productIdSet.add(qrPosterEntity.getTargetId());
            } else if (qrPosterForwardTypeEnum == QRPosterForwardTypeEnum.ARTICLE) {
                articleIdSet.add(qrPosterEntity.getTargetId());
            } else if (qrPosterForwardTypeEnum == QRPosterForwardTypeEnum.CUSTOMIZED_FORM) {
                customizeFormIdSet.add(qrPosterEntity.getTargetId());
            } else if (qrPosterForwardTypeEnum == QRPosterForwardTypeEnum.CONFERENCE_DETAIL) {
                activityIdSet.add(qrPosterEntity.getTargetId());
            } else if (qrPosterForwardTypeEnum == QRPosterForwardTypeEnum.CONFERENCE_INVITATION) {
                conferenceInvitationIdSet.add(qrPosterEntity.getTargetId());
            } else if (qrPosterForwardTypeEnum == QRPosterForwardTypeEnum.WX_OFFICIAL_ACCOUNT) {
                String[] strings = qrPosterEntity.getTargetId().split(",");
                if (strings.length > 1) {
                    String wxOfficialAccountAppId = strings[0];
                    if (StringUtils.isNotEmpty(wxOfficialAccountAppId)) {
                        wxOfficialAccountIdSet.add(wxOfficialAccountAppId);
                    }
                    String qrCodeId = strings[1];
                    if (StringUtils.isNotEmpty(qrCodeId)) {
                        wxOfficialAccountQRIdSet.add(Long.valueOf(strings[1]));
                    }
                }
            } else if (qrPosterForwardTypeEnum == QRPosterForwardTypeEnum.CONFERENCE_FORM) {
                conferenceFormIdSet.add(qrPosterEntity.getTargetId());
            }
        }
    }

    public String getForwardContent(Object id, QRPosterForwardTypeEnum qrPosterForwardTypeEnum) {
        if (qrPosterForwardTypeEnum == QRPosterForwardTypeEnum.PRODUCT) {
            ProductEntity productEntity = productEntityMap.get(id);
            if (null != productEntity) {
                return productEntity.getName();
            }
        } else if (qrPosterForwardTypeEnum == QRPosterForwardTypeEnum.ARTICLE) {
            ArticleEntity articleEntity = articleEntityMap.get(id);
            if (null != articleEntity) {
                return articleEntity.getTitle();
            }
        } else if (qrPosterForwardTypeEnum == QRPosterForwardTypeEnum.CONFERENCE_DETAIL) {
            ActivityEntity activityEntity = activityEntityMap.get(id);
            if (null != activityEntity) {
                return activityEntity.getTitle();
            }
        } else if (qrPosterForwardTypeEnum == QRPosterForwardTypeEnum.CONFERENCE_INVITATION) {
            ConferenceInvitationEntity conferenceInvitationEntity = conferenceInvitationEntityMap.get(id);
            if (null != conferenceInvitationEntity) {
                return conferenceInvitationEntity.getName();
            }
        } else if (qrPosterForwardTypeEnum == QRPosterForwardTypeEnum.CUSTOMIZED_FORM) {
            CustomizeFormDataEntity customizeFormDataEntity = customizeFormEntityMap.get(id);
            if (null != customizeFormDataEntity) {
                return customizeFormDataEntity.getFormHeadSetting().getName();
            }
        } else if (qrPosterForwardTypeEnum == QRPosterForwardTypeEnum.URL) {
            QRCodeEntity qrCodeEntity = qrCodeEntityMap.get(id);
            if (qrCodeEntity != null && StringUtils.isNotEmpty(qrCodeEntity.getValue())) {
                Map<String, Object> valueMap = GsonUtil.fromJson(qrCodeEntity.getValue(), Map.class);
                if (valueMap != null && valueMap.containsKey("url")) {
                    return (String) valueMap.get("url");
                }
            }

        } else if (qrPosterForwardTypeEnum == QRPosterForwardTypeEnum.CONFERENCE_FORM) {
            CustomizeFormDataEntity customizeFormDataEntity = conferenceFormEntityMap.get(id);
            if (null != customizeFormDataEntity) {
                return customizeFormDataEntity.getFormHeadSetting().getName();
            }
        }

        return null;
    }

    public void setQueryQRPosterByEaListNormalResult(QRPosterEntity qrPosterEntity, QueryQRPosterByEaListUnitResult result) {
        result.setQrPosterId(qrPosterEntity.getId());
        result.setQrCodeId(qrPosterEntity.getQrCodeId());
        result.setTitle(qrPosterEntity.getTitle());
        result.setBgUrl(fileV2Manager.getUrlByPath(qrPosterEntity.getBgApath(), qrPosterEntity.getEa(), false));
        result.setQrPosterUrl(fileV2Manager.getUrlByPath(qrPosterEntity.getApath(), qrPosterEntity.getEa(), false));
        result.setQrPosterThumbnailUrl(fileV2Manager.getUrlByPath(qrPosterEntity.getThumbnailApath(), qrPosterEntity.getEa(), false));
        if (!fileV2Manager.getSpreadContentDomain(qrPosterEntity.getEa()).equals(host)){
            result.setBgUrl(result.getBgUrl().replace(host, fileV2Manager.getCustomizeHost(qrPosterEntity.getEa())));
            result.setQrPosterUrl(result.getQrPosterUrl().replace(host, fileV2Manager.getCustomizeHost(qrPosterEntity.getEa())));
            result.setQrPosterThumbnailUrl(result.getQrPosterUrl().replace(host, fileV2Manager.getCustomizeHost(qrPosterEntity.getEa())));
        }
        result.setQrPosterApath(qrPosterEntity.getApath());
        result.setQrPosterThumbnailApath(qrPosterEntity.getThumbnailApath());
        result.setQrStyle(qrPosterEntity.getQrStyle());
        result.setCreateTime(qrPosterEntity.getCreateTime().getTime());
        result.setPosterStyle(qrPosterEntity.getPosterStyle());


        result.setMarketingEventId(qrPosterEntity.getMarketingEventId());
        if (marketingEventDataMap != null) {
            MarketingEventData marketingEventData = marketingEventDataMap.get(qrPosterEntity.getMarketingEventId());
            if (marketingEventData != null) {
                result.setMarketingEventTitle(marketingEventData.getName());
            }
        }

        if (null != qrPosterEntity.getQrCodeId()) {
            QRCodeEntity qrCodeEntity = qrCodeEntityMap.get(qrPosterEntity.getQrCodeId());
            if (null != qrCodeEntity) {
                result.setQrCodeAuthCode(qrCodeEntity.getAuthCode());
            }
        }
        result.setIsMobileDisplay(qrPosterEntity.getIsMobileDisplay());
    }

    public void setQueryQRPosterByEaListForwardResult(QRPosterEntity qrPosterEntity, QueryQRPosterByEaListUnitResult result) {
        String forwardName = "";
        String targetId = null;
        Object id = null;
        QRPosterForwardTypeEnum qrPosterForwardTypeEnum = QRPosterForwardTypeEnum.getByType(qrPosterEntity.getForwardType());
        if (qrPosterForwardTypeEnum != null) {
            if (QRPosterForwardTypeEnum.isInlineMaterial(qrPosterForwardTypeEnum)) {
                forwardName = I18nUtil.get(I18nKeyEnum.MARK_QR_QRPOSTERMANAGER_481);
                targetId = qrPosterEntity.getTargetId();
                id = qrPosterEntity.getTargetId();
            } else if (qrPosterForwardTypeEnum == QRPosterForwardTypeEnum.WX_OFFICIAL_ACCOUNT) {
                String[] strings = qrPosterEntity.getTargetId().split(",");
                if (strings.length > 1) {
                    String wxOfficialAccountAppId = strings[0];
                    if (!EmptyUtil.isNullForList(wxOfficialAccountAppId, wxOfficialAccountMap.get(wxOfficialAccountAppId).getWxAppName())) {
                        forwardName = wxOfficialAccountMap.get(wxOfficialAccountAppId).getWxAppName();
                    }
                    String qrCodeId = strings[1];
                    QrCodeResult qrCodeResult =  wxQRCodeMap.get(Long.valueOf(qrCodeId));
                    if (!EmptyUtil.isNullForList(qrCodeId, qrCodeResult)) {
                        String qrCodeName = qrCodeResult.getQrCodeName();
                        if (StringUtils.isNotEmpty(forwardName)) {
                            forwardName = forwardName + " " + qrCodeName;
                        } else {
                            forwardName = qrCodeName;
                        }
                    }
                }
            } else if (qrPosterForwardTypeEnum == QRPosterForwardTypeEnum.URL) {
                forwardName = I18nUtil.get(I18nKeyEnum.MARK_QR_QRPOSTERMANAGER_503);
                id = qrPosterEntity.getQrCodeId();
            }

            result.setForwardName(forwardName);
            result.setForwardContent(getForwardContent(id, qrPosterForwardTypeEnum));
            result.setForwardType(qrPosterEntity.getForwardType());
            result.setTargetId(targetId);
            result.setIsMobileDisplay(qrPosterEntity.getIsMobileDisplay());
        }
    }

    public void setQRPosterBriefDataNormalResult(QRPosterEntity qrPosterEntity, QRPosterBriefData result) {
        result.setObjectType(ObjectTypeEnum.QR_POSTER.getType());
        result.setId(qrPosterEntity.getId());
        result.setCreateTime(qrPosterEntity.getCreateTime().getTime());

        result.setTitle(qrPosterEntity.getTitle());
        result.setBgThumbnailUrl(fileV2Manager.getUrlByPath(qrPosterEntity.getThumbnailApath(), qrPosterEntity.getEa(), false));

        result.setQrPosterApath(qrPosterEntity.getApath());
        result.setQrPosterThumbnailApath(qrPosterEntity.getThumbnailApath());

        result.setMarketingEventId(qrPosterEntity.getMarketingEventId());
        if (marketingEventDataMap != null) {
            MarketingEventData marketingEventData = marketingEventDataMap.get(qrPosterEntity.getMarketingEventId());
            if (marketingEventData != null) {
                result.setMarketingEventTitle(marketingEventData.getName());
            }
        }
    }

    public void setQRPosterBriefDataForwardResult(QRPosterEntity qrPosterEntity, QRPosterBriefData result) {
        String forwardName = "";
        Object id = null;
        QRPosterForwardTypeEnum qrPosterForwardTypeEnum = QRPosterForwardTypeEnum.getByType(qrPosterEntity.getForwardType());
        if (qrPosterForwardTypeEnum != null) {
            if (QRPosterForwardTypeEnum.isInlineMaterial(qrPosterForwardTypeEnum)) {
                forwardName = I18nUtil.get(I18nKeyEnum.MARK_QR_QRPOSTERMANAGER_481);
                id = qrPosterEntity.getTargetId();
            } else if (qrPosterForwardTypeEnum == QRPosterForwardTypeEnum.WX_OFFICIAL_ACCOUNT) {
                String[] strings = qrPosterEntity.getTargetId().split(",");
                if (strings.length > 1) {
                    String wxOfficialAccountAppId = strings[0];
                    if (!EmptyUtil.isNullForList(wxOfficialAccountAppId, wxOfficialAccountMap.get(wxOfficialAccountAppId).getWxAppId())) {
                        forwardName = wxOfficialAccountMap.get(wxOfficialAccountAppId).getWxAppName();
                    }
                    String qrCodeId = strings[1];
                    QrCodeResult qrCodeResult =  wxQRCodeMap.get(Long.valueOf(qrCodeId));
                    if (!EmptyUtil.isNullForList(qrCodeId, qrCodeResult)) {
                        String qrCodeName = qrCodeResult.getQrCodeName();
                        if (!EmptyUtil.isNull(qrCodeName)) {
                            if (StringUtils.isNotEmpty(forwardName)) {
                                forwardName = forwardName + " " + qrCodeName;
                            } else {
                                forwardName = qrCodeName;
                            }
                        }
                    }
                }
            } else if (qrPosterForwardTypeEnum == QRPosterForwardTypeEnum.URL) {
                forwardName = I18nUtil.get(I18nKeyEnum.MARK_QR_QRPOSTERMANAGER_503);
                id = qrPosterEntity.getQrCodeId();
            }

            result.setForwardName(forwardName);
            result.setForwardContent(getForwardContent(id, qrPosterForwardTypeEnum));
        }
    }
}