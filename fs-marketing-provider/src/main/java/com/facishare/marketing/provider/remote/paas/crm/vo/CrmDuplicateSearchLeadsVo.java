package com.facishare.marketing.provider.remote.paas.crm.vo;

import com.facishare.marketing.provider.remote.paas.crm.vo.base.CrmDuplicateSearchVo;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

@Data
@ToString(callSuper = true)
public class CrmDuplicateSearchLeadsVo extends CrmDuplicateSearchVo {
    private static final long serialVersionUID = 1L;
    private Leads[] objects;

    @Data
    public static class Leads {
        @SerializedName("ObjectID")
        private String id;
        private String recordType;
        private String salesClueName;
        private String company;
        private String source;
        private String contactWay;
    }
}
