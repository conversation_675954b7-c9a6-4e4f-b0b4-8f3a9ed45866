package com.facishare.marketing.provider.remote;

import com.facishare.marketing.provider.remote.arg.LeadTransferArg;
import com.facishare.marketing.provider.remote.arg.MarkLeadToMQLArg;
import com.facishare.marketing.provider.remote.result.RestResult;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

@RestResource(value = "FS_CRM_SFA_INNER", desc = "SFA服务", contentType = "application/json")
public interface SfaCrmService {

    @POST("/LeadsObj/action/Transfer")
    RestResult<Map<String, Object>> leadTransfer(@HeaderMap Map<String, Object> header, @Body LeadTransferArg arg);

    @POST("/LeadsObj/action/MarkMQL")
    RestResult<Map<String, Object>> markLeadToMQL(@HeaderMap Map<String, Object> header, @Body MarkLeadToMQLArg arg);
}
