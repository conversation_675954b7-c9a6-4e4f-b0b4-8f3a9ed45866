package com.facishare.marketing.provider.remote.ai;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RepositoryQueryArg implements Serializable {

    private Float minScore;
    private Integer topK;
    private String content;

    public RepositoryQueryArg(String content) {
        this.minScore = 0.8F;
        this.topK = 5;
        this.content = content;
    }

}
