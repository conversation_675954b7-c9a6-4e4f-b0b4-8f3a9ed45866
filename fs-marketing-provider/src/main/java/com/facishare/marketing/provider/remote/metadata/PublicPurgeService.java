package com.facishare.marketing.provider.remote.metadata;

import com.fxiaoke.crmrestapi.arg.BulkHangTagForDataByIdArg;
import com.fxiaoke.crmrestapi.common.result.MetadataTagResult;
import com.fxiaoke.retrofit2.http.Body;
import com.fxiaoke.retrofit2.http.HeaderMap;
import com.fxiaoke.retrofit2.http.POST;
import com.fxiaoke.retrofitspring.annotation.RetrofitConfig;

import java.util.Map;
import java.util.Set;

@RetrofitConfig(baseUrl = "PaasMetadataTag")
public interface PublicPurgeService {

    @POST("paas/metadata/describe/batch/purge/apiName/syn")
    BaseMetaDataResult<PublicResult> batchPurge(@HeaderMap PurgeHeaderObj head, @Body BatchPurgeArg arg);

    @POST("paas/metadata/tagDataRelation/bulkAppendTagForData")
    MetadataTagResult<Boolean> bulkAppendTagForData(@Body BulkHangTagForDataByIdArg var1);
}
