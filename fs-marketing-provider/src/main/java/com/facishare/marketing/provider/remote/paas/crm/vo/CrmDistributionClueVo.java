package com.facishare.marketing.provider.remote.paas.crm.vo;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

@Data
public class CrmDistributionClueVo implements Serializable {
    @SerializedName("_id")
    private String id;

    @SerializedName("name")
    private String objectName;

    @SerializedName("field_Y13pN__c")
    private String clueName;

    @SerializedName("field_oSm5R__c")
    private String companyName;

    @SerializedName("field_1Qjlx__c")
    private String phone;

    @SerializedName("field_rj2U2__c")
    private String vocation;

    @SerializedName("field_21S04__c")
    private String email;

    @SerializedName("field_ULO63__c")
    private String detail;

    @SerializedName("field_WPydo__c")
    private String sdrId;  // 关联的SDR自定义对象

    @SerializedName("field_6IA61__c")
    private String validValue;  // 线索有效性

    @SerializedName("field_TM685__c")
    private String invalidDesc;  // 无效原因
}
