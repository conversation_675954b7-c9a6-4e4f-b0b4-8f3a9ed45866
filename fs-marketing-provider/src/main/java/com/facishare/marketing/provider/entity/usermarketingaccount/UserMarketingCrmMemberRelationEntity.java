package com.facishare.marketing.provider.entity.usermarketingaccount;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class UserMarketingCrmMemberRelationEntity extends BaseEaEntity implements Serializable  {
    private String id;
    
    private String userMarketingId;
    private String crmMemberObjectId;
    private Date createTime;
    private Date updateTime;
    private String userName;
    private String email;
}
