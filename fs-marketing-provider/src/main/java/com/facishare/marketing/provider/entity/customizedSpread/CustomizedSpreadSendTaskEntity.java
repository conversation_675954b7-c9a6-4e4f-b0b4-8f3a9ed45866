package com.facishare.marketing.provider.entity.customizedSpread;

import com.facishare.marketing.common.enums.whatsapp.SendTypeEnum;
import com.facishare.marketing.common.enums.whatsapp.TaskSendStatusEnum;
import com.facishare.marketing.provider.entity.BaseEntity;
import lombok.Data;

import java.util.Date;

@Data
public class CustomizedSpreadSendTaskEntity extends BaseEntity {

    /**
     * 发送的类型
     * @see SendTypeEnum
     */
    private String sendType;

    /**
     * 发送时间
     */
    private Date sendTime;

    /**
     * @see TaskSendStatusEnum
     */
    private String sendStatus;

    /**
     * 自定义通道id
     */
    private String customizedSpreadChannelId;

    private Integer createBy;
    private Integer updateBy;
}
