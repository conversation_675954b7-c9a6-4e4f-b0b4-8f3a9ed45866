package com.facishare.marketing.provider.crowd.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class MarketingEventMarketingUserGroupRelationEntity extends BaseEaEntity implements Serializable  {

    private String id;
    
    private String parentMarketingEventId;
    private String marketingEventId;
    private String marketingUserGroupId;
    private Date createTime;
    private Integer createBy;
    private Date updateTime;
    private Integer updateBy;

}
