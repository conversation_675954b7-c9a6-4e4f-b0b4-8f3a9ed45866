package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class BoardCardListEntity extends BaseEaEntity implements Serializable  {
    private String id;
    
    private String boardId;
    private String name;
    private Integer creator;
    //0表示新建状态（数据库默认状态）、1表示删除状态
    private int lifeStatus;
    private Date createTime;
    private Date updateTime;
}
