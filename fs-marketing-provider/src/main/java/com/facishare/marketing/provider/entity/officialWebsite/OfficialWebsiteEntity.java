package com.facishare.marketing.provider.entity.officialWebsite;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * Created  By zhoux 2019/11/25
 **/
@Data
public class OfficialWebsiteEntity extends BaseEaEntity implements Serializable  {

    private String id;

    

    private String websiteName;

    private String websiteUrl;

    private Integer status;

    private Integer createBy;

    private Date createTime;

    private Integer updateBy;

    private Date updateTime;

}
