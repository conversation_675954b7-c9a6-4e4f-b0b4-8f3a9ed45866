package com.facishare.marketing.provider.manager.feed;

import com.facishare.marketing.api.data.material.QRPosterBriefData;
import com.facishare.marketing.common.enums.ObjectTypeEnum;
import com.facishare.marketing.common.enums.qr.QRPosterTypeEnum;
import com.facishare.marketing.provider.dao.qr.QRPosterDAO;
import com.facishare.marketing.provider.entity.qr.QRPosterEntity;
import com.facishare.marketing.provider.manager.FileV2Manager;
import com.facishare.marketing.provider.manager.qr.QRPosterManager;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019/7/22.
 */
@Component
public class QRPosterMaterailDataManager extends MaterailDataManager<QRPosterBriefData>  {
    @Autowired
    private QRPosterDAO qrPosterDAO;
    @Autowired
    private QRPosterManager qrPosterManager;
    @Autowired
    private FileV2Manager fileV2Manager;

    @Override
    public List<QRPosterBriefData> get(String ea,String... objectIds) {
        List<QRPosterBriefData> datas = Lists.newArrayList();
        List<QRPosterEntity> qrPosterEntityList = qrPosterDAO.getByIds(Lists.newArrayList(Lists.newArrayList(objectIds)));
        if (CollectionUtils.isNotEmpty(qrPosterEntityList)) {
            // 根据海报ID批量查询市场活动标题
            qrPosterManager.batchQueryMarketingEventInfo(qrPosterEntityList.get(0).getEa(), qrPosterEntityList.stream().map(QRPosterEntity::getMarketingEventId).collect(Collectors.toList()));
            // 批量查询各物料详情
            qrPosterManager.batchGetMaterialByIds(qrPosterEntityList.get(0).getEa(), qrPosterEntityList);
        }
        List<String> imageUrls = qrPosterEntityList.stream().filter(Objects::nonNull)
                .map(QRPosterEntity::getApath)
                .filter(StringUtils::isNotBlank).collect(Collectors.toList());
        for (QRPosterEntity qrPosterEntity : qrPosterEntityList) {
            QRPosterBriefData data = new QRPosterBriefData();
            qrPosterManager.setQRPosterBriefDataNormalResult(qrPosterEntity, data);
            qrPosterManager.setQRPosterBriefDataForwardResult(qrPosterEntity, data);
            data.setCreator(qrPosterEntity.getCreateBy());
            data.setQrPostForwardType(qrPosterEntity.getForwardType());
            datas.add(data);
        }
        //多线程处理图片
        Map<String, Long> coverMap = fileV2Manager.processImageSizes(ea, imageUrls);
        datas.forEach(data -> {
            if (data.getQrPosterApath() != null && coverMap.containsKey(data.getQrPosterApath())) {
                data.setCoverSize(coverMap.get(data.getQrPosterApath()));
            }
        });
        return datas;
    }

    @Override
    public Integer getType() {
        return ObjectTypeEnum.QR_POSTER.getType();
    }
}
