package com.facishare.marketing.provider.remote.paas.extractor;

import com.facishare.marketing.provider.remote.paas.crm.CrmBusinessException;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

/**
 * <AUTHOR>
 */
public class InnerV1DataExtractor extends AbstractDataExtractor {
    private static final DataExtractor INSTANCE = new InnerV1DataExtractor();
    private static final String SUCCESS_CODE = "0";

    private InnerV1DataExtractor() {
    }

    public static DataExtractor getInstance() {
        return INSTANCE;
    }

    @Override
    final JsonElement extractRealData(JsonElement jsonElement) {
        JsonObject result = jsonElement.getAsJsonObject();
        String errorCode = result.get("errCode").getAsString();
        if (!SUCCESS_CODE.equals(errorCode)) {
            String errorDetail = result.get("errMessage").getAsString();
            throw new CrmBusinessException(Integer.valueOf(errorCode), errorDetail);
        }
        return result.get("result");
    }
}
