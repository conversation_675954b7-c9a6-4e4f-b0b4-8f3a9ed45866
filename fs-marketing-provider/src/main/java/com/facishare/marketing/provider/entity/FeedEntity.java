package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName FeedEntity
 * @Description
 * <AUTHOR>
 * @Date 2019/2/26 2:42 PM
 */
@Data
@Entity
public class FeedEntity extends BaseEaEntity implements Serializable  {
    private String id;
    private String uid;
    /**
     * 动作类型，见{@link com.facishare.mankeep.common.enums.ActionTypeEnum}
     */
    //行为描述：1文章，2图片,3名片
    private Integer actionType;

    // 推荐语
    private String recommendation;
    // 标题
    private String title;
    //摘要
    private String summary;
    //封面或图片
    private String photoUrl;
    //封面缩略图
    private String photoThumbnailUrl;
    // 创建时间
    private Date createTime;
    //类别：1文章，2图片
    private Integer objectType;
    //文章id,分享图片id
    private String objectId;
    //是否可见：1可见，2不可见
    private Integer visible;
    // 前端生成的文章索引，查询详情时用到
    private String articleKey;
    private String feedKey;
    //1自己推广 2别人推广
    private Integer spreadType;
    //群id
    private String groupId;
    //别人帮你推广的uid
    private String spreadUid;

    private String fileSize;
    //文件id
    private String fileId;
    //文件名
    private String fileName;
    //文件后缀名
    private String fileExtension;
    //网盘文件的nPath或者TAPath
    private String fsPath;
    //文件所属公司的ea
    
    //文件是否支持预览
    private Integer isCanPreview;
    //文件为图片时返回，图片原始大图url
    private String imgUrl;

    //载体[文章，产品]：1红包
    private Integer luckMoney;
}
