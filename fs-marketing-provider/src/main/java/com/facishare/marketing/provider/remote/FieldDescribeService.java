package com.facishare.marketing.provider.remote;

import com.facishare.marketing.provider.remote.arg.AssignRecordArg;
import com.facishare.marketing.provider.remote.arg.FindRoleAndRecordTypeArg;
import com.facishare.marketing.provider.remote.rest.arg.*;
import com.facishare.marketing.provider.remote.rest.result.FindDescribeResult;
import com.facishare.marketing.provider.remote.result.CrmListPage;
import com.facishare.marketing.provider.remote.result.FindRoleAndRecordTypeResult;
import com.facishare.marketing.provider.remote.result.SfaIntegralResult;
import com.fxiaoke.crmrestapi.arg.ControllerListArg;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.Page;
import com.fxiaoke.crmrestapi.common.result.Result;
import com.fxiaoke.retrofit2.http.Body;
import com.fxiaoke.retrofit2.http.HeaderMap;
import com.fxiaoke.retrofit2.http.POST;
import com.fxiaoke.retrofit2.http.Path;
import com.fxiaoke.retrofitspring.annotation.RetrofitConfig;

@RetrofitConfig(baseUrl = "PaasMetadata")
public interface FieldDescribeService {

    @POST("v1/rest/object/describe/service/disableCustomField")
    Result disableCustomField(@HeaderMap HeaderObj head, @Body CustomFieldArg arg);

    @POST("v1/rest/object/describe/service/deleteDescribeCustomField")
    Result deleteDescribeCustomField(@HeaderMap HeaderObj head, @Body CustomFieldArg arg);

    @POST("v1/rest/object/describe/service/findDescribeByApiName")
    Result<FindDescribeResult> findDescribeByApiName(@HeaderMap HeaderObj head, @Body FindDescribeArg arg);

    @POST("v1/rest/object/describe/service/updateDescribe")
    Result updateDescribe(@HeaderMap HeaderObj head, @Body UpdateDescribeArg arg);

    // https://crm.ceshi112.com/FHH/EM1HNCRM/API/v1/object/layout/service/findByObjDescribeApiName
    // 这里是查询列表页布局，注意：不是移动端的
    @POST("v1/rest/object/layout/service/findByObjDescribeApiName")
    Result findListLayout(@HeaderMap HeaderObj head, @Body FindLayoutListArg arg);

    // https://crm.ceshi112.com/FHH/EM1HNCRM/API/v1/object/layout/service/updateWebLayoutAndUpdateDescribe
    // 更新列表页布局，注意： 不是移动端的
    @POST("v1/rest/object/layout/service/updateWebLayoutAndUpdateDescribe")
    Result updateListLayout(@HeaderMap HeaderObj head, @Body UpdateLayoutListArg arg);

    @POST("v1/rest/object/{apiName}/controller/List")
    Result<CrmListPage<ObjectData>> list(@HeaderMap HeaderObj head, @Path("apiName") String apiName, @Body ControllerListArg arg);

    @POST("v1/object/record_type/service/assignRecord")
    SfaIntegralResult assignRecord(@HeaderMap HeaderObj head, @Body AssignRecordArg arg);

    @POST("v1/object/record_type/service/findRoleAndRecordType")
    FindRoleAndRecordTypeResult findRoleAndRecordType(@HeaderMap HeaderObj head, @Body FindRoleAndRecordTypeArg arg);

}
