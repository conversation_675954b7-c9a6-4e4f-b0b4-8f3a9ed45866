package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Entity;
import lombok.Data;

@Data
@Entity
public class ArticleEntity extends BaseEaEntity implements Serializable  {
    private String id;
    private String uid;
    // 封面
    private String photoId;
    private String photoUrl;
    private String photoThumbnailUrl;
    private String photoApath;
    private String photoThumbnailApath;
    // 标题
    private String title;
    // 摘要
    private String summary;
    // 文章路径
    private String articlePath;
    // 文章内容
    private String content;
    // 文章类型 1:原创 2:转载
    private Integer articleType;
    // 作者
    private String creator;
    // 来源
    private String source;
    // 来源类型 1:公众号 2：自定义
    private Integer sourceType;
    // 状态 -1:未启用 1:已启用 4:已停用 -5已删除
    private Integer status;
    // 文章图片和纷享图片的对应关系json串
    private String pictureJson;
    // 纷享企业账号
    
    // 纷享个人账号
    private Integer fsUserId;
    // 文章链接
    private String url;
    // 创建时间瓬
    private Date createTime;
    // 更新时间
    private Date lastModifyTime;
    // 推荐语
    private String recommendation;

    /**
     * 进群卡片封面图
     */
    private String cardPhotoUrl;

    private String lmCoverUrl;

    // 支持小程序文章展示 前缀(包含 title, updateTime, creator)
    private String preArticleContent;

    //文章后台解析的apath文件地址
    private String parsedContentPath;

    /**
     * 创建来源
     * {@link com.facishare.marketing.common.enums.ArticleCreateSourceTypeEnum}
     */
    private Integer createSourceType;

    //分享海报Apath
    private String sharePosterAPath;

    private int scopeType;
}
