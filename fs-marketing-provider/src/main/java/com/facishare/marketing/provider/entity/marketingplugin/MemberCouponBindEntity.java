package com.facishare.marketing.provider.entity.marketingplugin;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/15 18:22
 */
@Entity
@Data
public class MemberCouponBindEntity extends BaseEaEntity implements Serializable  {
    private String id;
    
    private String createTime;
    private String unionid;
    private String partnerId;
    private String accountId;
    private String couponId;
    private String salesOrderId;
    private String attachInfo;
    private String memberId;
    private String sendChannel;
    private String couponCode;
    private String openid;
    private String sendMerchant;
    private String sendTime;
    private String appid;
    private String status;
    private String couponInstanceId; //优惠券实例id
}
