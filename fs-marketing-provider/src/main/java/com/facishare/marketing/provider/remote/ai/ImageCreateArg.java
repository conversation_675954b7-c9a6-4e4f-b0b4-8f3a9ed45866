package com.facishare.marketing.provider.remote.ai;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

@Data
public class ImageCreateArg implements Serializable {
    protected String prompt;
    private Integer n = 1;//默认1
    private String size = "1024x1024";//默认512x512; 256x256 512x512 1024x1024
    @SerializedName("response_format")
    private String responseFormat = "b64_json";//默认url
    private String model = "DALL·E2";
}
