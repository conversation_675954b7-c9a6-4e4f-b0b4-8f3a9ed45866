package com.facishare.marketing.provider.remote.whatsapp.result;

import com.facishare.marketing.common.enums.whatsapp.*;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TemplateResult implements Serializable {

    private List<TemplateInfo> data;

    private PageInfo paging;

    @Data
    public static class TemplateInfo implements Serializable {
        /**
         * 模板ID
         */
        private String id;
        /**
         * 模板类型
         * @see TemplateCategoryEnum
         */
        private String category;
        /**
         * 模板语言
         */
        private String language;
        /**
         * 模板名称
         */
        private String name;
        /**
         * 审核状态枚举
         * @see com.facishare.marketing.common.enums.whatsapp.TemplateStatusEnum
         */
        private String status;
        /**
         * 拒绝原因
         */
        @SerializedName("rejected_reason")
        private String rejectedReason;
        /**
         * 模板质量
         * @see QualityScoreEnum
         */
        @SerializedName("quality_score")
        private QualityScore qualityScore;
        /**
         * 模板组件
         */
        private List<Component> components;
    }

    @Data
    public static class Component implements Serializable {
        /**
         * 组件类型
         * @see TemplateComponentTypeEnum
         */
        private String type;
        /**
         * 仅type= HEADER时有此项且为必须项，type!=HEADER时，无此项。
         * 描述HEADER里内容的类型。
         * @see ComponentFormatEnum
         */
        private String format;
        /**
         * type= HEADER且format=text时，为必须项；type=BODY时，为必须项；
         * type=FOOTER时，为必须项；type=BUTTONS时，无此项。
         */
        private String text;
        /**
         * 变量示例，当HEADER或者BODY内容中配置了变量时，为必须项。否则无此项。
         */
        private Example example;
        /**
         * 仅type=BUTTONS时，为必须项，否则无此项。
         */
        private List<Button> buttons;
        /**
         * 添加安全建议 身份验证模板 type = BODY 时可选 值为 true/false。
         */
        @SerializedName("add_security_recommendation")
        private Boolean addSecurityRecommendation;
        /**
         * 验证码的失效时间 身份验证模板 type = FOOTER 时可选 值的可填范围是 1-90。
         */
        @SerializedName("code_expiration_minutes")
        private Integer codeExpirationMinutes;
    }

    @Data
    public static class Button implements Serializable {
        /**
         * 按钮类型，
         * @see ButtonTypeEnum
         */
        private String type;
        /**
         * 按钮的显示文字
         */
        private String text;
        /**
         * 行动号召按钮上配置的网址，仅type=URL时为必须项。如果是动态url，则需带上后缀变量1。
         */
        private String url;
        /**
         * 行动号召按钮上配置的电话，仅type= PHONE_NUMBER时为必须项
         */
        @SerializedName("phone_number")
        private String phoneNumber;
        /**
         * 示例的URL, type=URL，且为动态url时为必须项，例如:https://www.baidu.com/user
         */
        private List<String> example;
        /**
         * 验证码按钮类型 固定值 ONE_TAP 或者 COPY_CODE ；ONE_TAP的含义是一键填充 COPY_CODE的含义是复制验证码
         * @see ButtonOtpTymeEnum
         */
        @SerializedName("otp_type")
        private String otpType;
        /**
         * 自动填充按钮文本，展示在按钮上的文案，仅otp_type = ONE_TAP 时为必须项
         */
        @SerializedName("autofill_text")
        private String autofillText;
        /**
         * 安卓包名，仅otp_type = ONE_TAP 时为必须项
         */
        @SerializedName("package_name")
        private String packageName;
        /**
         * 应用hash，仅otp_type = ONE_TAP 时为必须项
         */
        @SerializedName("signature_hash")
        private String signatureHash;
    }

    @Data
    public static class Example implements Serializable {
        /**
         * 当HEADER中 format=DOCUMENT或format=IMAGE或format=VIDEO时，与custom_header_handle_url二选一，否则无此项，如果与custom_header_handle_url同时存在时，以custom_header_handle_url为准
         */
        @SerializedName("header_handle")
        private List<String> headerHandle;
        /**
         * 当HEADER中 format=DOCUMENT或format=IMAGE或format=VIDEO时，与header_handle二选一，否则无此项,示例媒体url建议小于1M，否则会超时。如果与header_handle同时存在时，以custom_header_handle_url为准
         */
        @SerializedName("custom_header_handle_url")
        private String customHeaderHandleUrl;
        /**
         * 当HEADER中 format=TEXT时为必须项，否则无此项
         */
        @SerializedName("header_text")
        private List<String> headerText;
        /**
         * 当BODY内容文本中配置了变量时为必须项，根据变量配置，数组中可能有1个或多个值。当BODY内容文本中未配置变量则无此项
         */
        @SerializedName("body_text")
        private List<List<String>> bodyText;

        private String type;

        private String filename;
    }

    @Data
    public static class QualityScore implements Serializable {
        /**
         * 模板质量枚举
         * 枚举值:
         * GREEN: 高 YELLOW: 中
         * RED: 低 UNKNOWN: 质量待定
         */
        private String score;
    }

    @Data
    public static class PageInfo {
        private String previous;
        private String next;
        private Cursors cursors;
    }

    @Data
    public static class Cursors {
        private String before;
        private String after;
    }


}
