package com.facishare.marketing.provider.remote.rest.result;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@Data
public class RemoteResult<T> implements Serializable {
    private String errorCode;
    private String errorMsg;
    private T data;

    public RemoteResult() {
    }

    public RemoteResult(String errorCode, String errorMsg) {
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    public RemoteResult(String errorCode, String errorMsg, T data) {
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
        this.data = data;
    }

    @JsonIgnore
    public boolean isSuccess() {
        return StringUtils.isNotBlank(errorMsg) && errorMsg.equals("succ");
    }
}
