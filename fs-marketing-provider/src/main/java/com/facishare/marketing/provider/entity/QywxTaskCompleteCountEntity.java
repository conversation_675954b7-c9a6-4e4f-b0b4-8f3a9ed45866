package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * Created on 2021-02-24.
 */
@Data
public class QywxTaskCompleteCountEntity extends BaseEaEntity implements Serializable  {
	private String marketingActivityId;
	private String triggerTaskInstanceId;
	private String qywxUserId;
	private Integer count;
	private Integer finishEmployee;
	private Integer finishCustomer;

}
