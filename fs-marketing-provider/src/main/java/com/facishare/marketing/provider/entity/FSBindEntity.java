package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
public class FSBindEntity extends BaseEaEntity implements Serializable  {
    /**
     *
     * **/
    private String uid;
    /**
     *手机号码
     * **/
    private String phone;
    /**
     *企业ea
     * **/
    
    /**
     *企业用户ID
     * **/
    private Integer fsUserId;
    /**
     *公司ID
     * **/
    private Integer fsCorpId;
    private Date createTime;
    private Date lastModifyTime;
    /**  * 绑定类型
     */
    private Integer type;  //AccountTypeEnum
    private String appId;
}
