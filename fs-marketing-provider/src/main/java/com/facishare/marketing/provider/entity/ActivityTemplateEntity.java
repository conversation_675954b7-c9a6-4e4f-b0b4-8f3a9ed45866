package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import com.facishare.marketing.common.typehandlers.value.ActivityTemplateDescriptionList;
import com.facishare.marketing.common.typehandlers.value.FieldInfoList;
import java.io.Serializable;
import javax.persistence.Entity;
import lombok.Data;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 02/09/2018
 */
@Data
@Entity
public class ActivityTemplateEntity extends BaseEaEntity implements Serializable  {
    private String id;
    private String coverImageUrl;
    private String backgroundImageUrl;
    private String coverImageSmallUrl;
    private String backgroundImageSmallUrl;
    /**
     * 活动模版描述
     */
    private ActivityTemplateDescriptionList coverTextDescribe;
    /**
     * 模板名称
     */
    private String templateName;
    /**
     * apiName
     */
    private String enrollApiName;
    /**
     * 对象信息
     */
    private FieldInfoList fieldDescribes;
    /**
     * 对象名称
     */
    private String displayName;
    /**
     * 欢迎语
     */
    private String welcomeMsg;
}
