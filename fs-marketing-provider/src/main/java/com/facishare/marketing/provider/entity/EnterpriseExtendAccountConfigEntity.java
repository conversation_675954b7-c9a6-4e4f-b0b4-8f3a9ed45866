package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import lombok.Data;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.Date;

@Data
@Entity
public class EnterpriseExtendAccountConfigEntity extends BaseEaEntity implements Serializable  {

    private String id;
    
    /**
     * com.facishare.marketing.common.enums.EnterpriseExtendAccountTypeEnum
     */
    private String accountType;
    private String accountId;
    private Date createTime;
    private Date updateTime;
}
