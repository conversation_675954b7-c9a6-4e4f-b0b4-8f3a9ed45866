package com.facishare.marketing.provider.entity;

import com.facishare.marketing.provider.entity.BaseEaEntity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class MiniappAuthorizePageEntity extends BaseEaEntity implements Serializable {
    private String id;
    
    private Integer userId;
    private String coverApath;
    private Integer useType;
    private Date createTime;
    private Date updateTime;
}
